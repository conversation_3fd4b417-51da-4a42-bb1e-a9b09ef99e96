import mongoose from 'mongoose';

// Define schemas
const achievementSchema = new mongoose.Schema({
  id: { type: String, required: true },
  title: { type: String, required: true },
  description: { type: String },
  date: { type: Date, default: Date.now },
  type: { type: String, enum: ['streak', 'level', 'rank', 'likes', 'task'] }
});

const rewardSchema = new mongoose.Schema({
  id: { type: String, required: true },
  title: { type: String, required: true },
  description: { type: String },
  date: { type: Date, default: Date.now },
  value: { type: Number },
  type: { type: String, enum: ['xp', 'badge', 'feature'] }
});

const taskSchema = new mongoose.Schema({
  completedTasks: [String],
  lastReset: { type: Date }
});

const userProfileSchema = new mongoose.Schema({
  anilistId: { type: String, required: true, unique: true },
  
  // Level and XP
  level: { type: Number, default: 1 },
  xp: { type: Number, default: 0 },
  rank: { type: String, default: 'Rookie' },
  
  // Streak tracking
  streak: { type: Number, default: 0 },
  longestStreak: { type: Number, default: 0 },
  lastVisit: { type: Date },
  visitDates: [Date],
  
  // Social features
  likes: { type: Number, default: 0 },
  likedBy: [String], // Array of anilistIds who liked this user
  likedProfiles: [String], // Array of anilistIds this user has liked
  
  // Achievements and rewards
  achievements: [achievementSchema],
  rewards: [rewardSchema],
  
  // Tasks
  tasks: { type: taskSchema, default: { completedTasks: [], lastReset: null } },
  
  // Metadata
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Update the updatedAt field on save
userProfileSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

const UserProfile = mongoose.model('UserProfile', userProfileSchema);

export default UserProfile;
