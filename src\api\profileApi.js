import axios from 'axios';

// Base API URL for the profile backend
const PROFILE_API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://profile-api.yourbackend.com/api'
  : 'http://localhost:3004/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: PROFILE_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests if available
api.interceptors.request.use(config => {
  // Get token from AniList storage
  const anilistStorage = localStorage.getItem('anilist');
  if (anilistStorage) {
    try {
      const anilistData = JSON.parse(anilistStorage);
      if (anilistData.access_token) {
        config.headers.Authorization = `Bearer ${anilistData.access_token}`;
      }
    } catch (error) {
      console.error('Error parsing AniList storage:', error);
    }
  }
  return config;
});

// User Profile API endpoints
const profileApi = {
  // User Profile endpoints
  getUserProfile: async (userId) => {
    try {
      const response = await api.get(`/profile/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  },

  updateUserProfile: async (userId, profileData) => {
    try {
      const response = await api.put(`/profile/${userId}`, profileData);
      return response.data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },

  recordVisit: async (userId) => {
    try {
      const response = await api.post(`/profile/${userId}/visit`);
      return response.data;
    } catch (error) {
      console.error('Error recording visit:', error);
      throw error;
    }
  },

  // XP and Level endpoints
  addXp: async (userId, amount, source) => {
    try {
      const response = await api.post(`/profile/${userId}/xp`, { amount, source });
      return response.data;
    } catch (error) {
      console.error('Error adding XP:', error);
      throw error;
    }
  },

  // Likes endpoints
  addLike: async (likerUserId, targetUserId) => {
    try {
      const response = await api.post(`/profile/${targetUserId}/like`, { likerUserId });
      return response.data;
    } catch (error) {
      console.error('Error adding like:', error);
      throw error;
    }
  },

  // Tasks endpoints
  getDailyTasks: async (userId) => {
    try {
      const response = await api.get(`/profile/${userId}/tasks`);
      return response.data;
    } catch (error) {
      console.error('Error fetching daily tasks:', error);
      throw error;
    }
  },

  completeTask: async (userId, taskId) => {
    try {
      const response = await api.post(`/profile/${userId}/tasks/${taskId}/complete`);
      return response.data;
    } catch (error) {
      console.error('Error completing task:', error);
      throw error;
    }
  },

  // Achievements endpoints
  unlockAchievement: async (userId, achievementId) => {
    try {
      const response = await api.post(`/profile/${userId}/achievements`, { achievementId });
      return response.data;
    } catch (error) {
      console.error('Error unlocking achievement:', error);
      throw error;
    }
  },

  // Leaderboard endpoints
  getLeaderboard: async (type = 'xp', limit = 10) => {
    try {
      const response = await api.get(`/leaderboard?type=${type}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      throw error;
    }
  },

  // Fallback methods for when API is unavailable
  // These use localStorage as a backup
  fallback: {
    getUserProfile: (userId) => {
      try {
        const streak = parseInt(localStorage.getItem(`streak-${userId}`)) || 0;
        const longestStreak = parseInt(localStorage.getItem(`longest-streak-${userId}`)) || 0;
        const lastVisit = parseInt(localStorage.getItem(`last-visit-${userId}`)) || null;
        const visitDates = JSON.parse(localStorage.getItem(`visit-dates-${userId}`)) || [];
        const level = parseInt(localStorage.getItem(`level-${userId}`)) || 1;
        const xp = parseInt(localStorage.getItem(`xp-${userId}`)) || 0;
        const rank = localStorage.getItem(`rank-${userId}`) || 'Rookie';
        const likes = parseInt(localStorage.getItem(`likes-${userId}`)) || 0;
        const achievements = JSON.parse(localStorage.getItem(`achievements-${userId}`)) || [];
        const rewards = JSON.parse(localStorage.getItem(`rewards-${userId}`)) || [];
        const completedTasks = JSON.parse(localStorage.getItem(`completed-tasks-${userId}`)) || [];

        return {
          streak,
          longestStreak,
          lastVisit,
          visitDates,
          level,
          xp,
          rank,
          likes,
          achievements,
          rewards,
          completedTasks,
          isFromFallback: true
        };
      } catch (error) {
        console.error('Error in fallback getUserProfile:', error);
        return {
          streak: 0,
          longestStreak: 0,
          lastVisit: null,
          visitDates: [],
          level: 1,
          xp: 0,
          rank: 'Rookie',
          likes: 0,
          achievements: [],
          rewards: [],
          completedTasks: [],
          isFromFallback: true
        };
      }
    },

    updateUserProfile: (userId, profileData) => {
      try {
        if (profileData.streak) localStorage.setItem(`streak-${userId}`, profileData.streak.toString());
        if (profileData.longestStreak) localStorage.setItem(`longest-streak-${userId}`, profileData.longestStreak.toString());
        if (profileData.lastVisit) localStorage.setItem(`last-visit-${userId}`, profileData.lastVisit.toString());
        if (profileData.visitDates) localStorage.setItem(`visit-dates-${userId}`, JSON.stringify(profileData.visitDates));
        if (profileData.level) localStorage.setItem(`level-${userId}`, profileData.level.toString());
        if (profileData.xp) localStorage.setItem(`xp-${userId}`, profileData.xp.toString());
        if (profileData.rank) localStorage.setItem(`rank-${userId}`, profileData.rank);
        if (profileData.likes) localStorage.setItem(`likes-${userId}`, profileData.likes.toString());
        if (profileData.achievements) localStorage.setItem(`achievements-${userId}`, JSON.stringify(profileData.achievements));
        if (profileData.rewards) localStorage.setItem(`rewards-${userId}`, JSON.stringify(profileData.rewards));
        if (profileData.completedTasks) localStorage.setItem(`completed-tasks-${userId}`, JSON.stringify(profileData.completedTasks));

        return { ...profileData, isFromFallback: true };
      } catch (error) {
        console.error('Error in fallback updateUserProfile:', error);
        return { isFromFallback: true, error: true };
      }
    },

    // Task-related fallback methods
    getDailyTasks: (userId) => {
      try {
        // Get the current date
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Get the last reset date
        const lastResetKey = `tasks-last-reset-${userId}`;
        const lastReset = localStorage.getItem(lastResetKey);

        // Check if we need to reset tasks for a new day
        if (!lastReset || parseInt(lastReset) < today.getTime()) {
          // Clear completed tasks for the new day
          localStorage.setItem(`completed-tasks-${userId}`, JSON.stringify([]));
          localStorage.setItem(lastResetKey, today.getTime().toString());

          return { completedTasks: [], isFromFallback: true };
        }

        // Get completed tasks
        const completedTasks = JSON.parse(localStorage.getItem(`completed-tasks-${userId}`)) || [];

        return { completedTasks, isFromFallback: true };
      } catch (error) {
        console.error('Error in fallback getDailyTasks:', error);
        return { completedTasks: [], isFromFallback: true, error: true };
      }
    },

    completeTask: (userId, taskId) => {
      try {
        // Get current completed tasks
        const completedTasksKey = `completed-tasks-${userId}`;
        const completedTasks = JSON.parse(localStorage.getItem(completedTasksKey)) || [];

        // Add the task if it's not already completed
        if (!completedTasks.includes(taskId)) {
          completedTasks.push(taskId);
          localStorage.setItem(completedTasksKey, JSON.stringify(completedTasks));
        }

        return { success: true, completedTasks, isFromFallback: true };
      } catch (error) {
        console.error('Error in fallback completeTask:', error);
        return { success: false, isFromFallback: true, error: true };
      }
    },

    addXp: (userId, amount, source) => {
      try {
        // Get current XP and level
        const currentXp = parseInt(localStorage.getItem(`xp-${userId}`)) || 0;
        const currentLevel = parseInt(localStorage.getItem(`level-${userId}`)) || 1;

        // Add XP
        const newXp = currentXp + amount;
        localStorage.setItem(`xp-${userId}`, newXp.toString());

        // Calculate new level
        let newLevel = currentLevel;
        while (Math.pow(newLevel + 1, 2) * 100 <= newXp) {
          newLevel++;
        }

        // Update level if changed
        if (newLevel > currentLevel) {
          localStorage.setItem(`level-${userId}`, newLevel.toString());
        }

        return {
          success: true,
          xp: newXp,
          level: newLevel,
          xpAdded: amount,
          source,
          isFromFallback: true
        };
      } catch (error) {
        console.error('Error in fallback addXp:', error);
        return { success: false, isFromFallback: true, error: true };
      }
    }
  }
};

export default profileApi;
