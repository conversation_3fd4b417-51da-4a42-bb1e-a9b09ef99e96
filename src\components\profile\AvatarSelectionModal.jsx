import { useState, useEffect } from "react";
import { X, Check, User, ChevronRight } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Avatar categories with their images
// These will be replaced with your CDN links
const AVATAR_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    avatars: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    avatars: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    avatars: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const AvatarSelectionModal = ({ isOpen, onClose, onSelect, currentAvatar }) => {
  const { user } = useAniList();
  const [selectedAvatar, setSelectedAvatar] = useState(null);
  const [activeCategory, setActiveCategory] = useState(AVATAR_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);

  // Set the current avatar as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentAvatar) {
      setSelectedAvatar(currentAvatar);
    }
  }, [isOpen, currentAvatar]);

  // Handle avatar selection
  const handleAvatarSelect = (avatarUrl) => {
    setSelectedAvatar(avatarUrl);
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedAvatar) return;

    setIsSaving(true);
    try {
      // Save the selected avatar to the user's profile
      await profileApi.updateProfilePicture(user.id, selectedAvatar);

      // Call the onSelect callback with the selected avatar
      onSelect(selectedAvatar);

      toast.success("Profile picture updated successfully!");
      onClose();
    } catch (error) {
      console.error("Error updating profile picture:", error);
      toast.error("Failed to update profile picture. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/95">
      <div className="w-full max-w-4xl overflow-hidden rounded-xl border border-white/5 shadow-[0_0_40px_rgba(0,0,0,0.9)] bg-gradient-to-br from-gray-900 to-black">
        {/* Header with close button */}
        <div className="relative h-16 flex items-center justify-center border-b border-white/5">
          <h2 className="text-lg font-bold text-white tracking-wide">PROFILE AVATAR</h2>
          <button
            onClick={onClose}
            className="absolute right-4 p-2 rounded-full hover:bg-white/5 transition-all duration-200 text-white/70 hover:text-white"
          >
            <X size={18} />
          </button>
        </div>

        <div className="flex flex-col md:flex-row h-[70vh]">
          {/* Left side - Preview */}
          <div className="md:w-1/3 border-r border-white/5 flex flex-col">
            <div className="flex-1 flex flex-col items-center justify-center p-8 relative">
              {/* Background pattern */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_white_0%,_transparent_60%)] opacity-20"></div>
                <div className="h-full w-full bg-[repeating-linear-gradient(45deg,_transparent,_transparent_10px,_rgba(255,255,255,0.05)_10px,_rgba(255,255,255,0.05)_20px)]"></div>
              </div>

              {/* Preview title */}
              <div className="mb-6 text-center">
                <span className="text-xs font-medium text-white/40 uppercase tracking-widest">Preview</span>
              </div>

              {/* Avatar preview */}
              <div className="relative mb-8 group">
                {selectedAvatar ? (
                  <div className="relative">
                    <div className="w-40 h-40 rounded-full overflow-hidden border-[3px] border-white/10 shadow-[0_0_25px_rgba(0,0,0,0.5)] relative group-hover:border-white/20 transition-all duration-300">
                      <img
                        src={selectedAvatar}
                        alt="Selected avatar"
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                    </div>
                    <div className="absolute -bottom-2 -right-2 bg-white p-1 rounded-full shadow-lg">
                      <Check size={16} className="text-black" />
                    </div>
                  </div>
                ) : (
                  <div className="w-40 h-40 rounded-full flex items-center justify-center bg-white/5 border-[3px] border-dashed border-white/10">
                    <User size={40} className="text-white/20" />
                  </div>
                )}
              </div>

              {/* User name */}
              <div className="text-center mb-8">
                <h3 className="text-white font-medium">{user?.name || "Your Profile"}</h3>
                <p className="text-white/40 text-xs mt-1">Select an avatar that represents you</p>
              </div>

              {/* Action button */}
              {selectedAvatar && (
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className={`w-full py-3 rounded-md bg-white text-black text-sm font-medium transition-all duration-200 hover:bg-white/90 ${
                    isSaving ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                >
                  {isSaving ? "Saving..." : "Apply Avatar"}
                </button>
              )}
            </div>
          </div>

          {/* Right side - Categories and Gallery */}
          <div className="md:w-2/3 flex flex-col">
            {/* Category tabs */}
            <div className="border-b border-white/5">
              <div className="flex">
                {AVATAR_CATEGORIES.map(category => (
                  <button
                    key={category.id}
                    className={`px-6 py-4 text-sm font-medium transition-all duration-200 relative ${
                      activeCategory === category.id
                        ? "text-white"
                        : "text-white/40 hover:text-white/70"
                    }`}
                    onClick={() => setActiveCategory(category.id)}
                  >
                    {category.name}
                    {activeCategory === category.id && (
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-white"></div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Gallery */}
            <div className="flex-1 p-6 overflow-y-auto scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
              <div className="grid grid-cols-3 sm:grid-cols-4 gap-4">
                {AVATAR_CATEGORIES.find(c => c.id === activeCategory)?.avatars.map(avatar => (
                  <div
                    key={avatar.id}
                    className="group"
                  >
                    <button
                      className={`relative rounded-lg overflow-hidden aspect-square transition-all duration-300 ${
                        selectedAvatar === avatar.url
                          ? "ring-2 ring-white shadow-[0_0_15px_rgba(255,255,255,0.2)]"
                          : "hover:shadow-[0_0_15px_rgba(255,255,255,0.1)] hover:scale-105"
                      }`}
                      onClick={() => handleAvatarSelect(avatar.url)}
                    >
                      <img
                        src={avatar.url}
                        alt={`Avatar ${avatar.id}`}
                        className="w-full h-full object-cover"
                      />
                      <div className={`absolute inset-0 flex items-center justify-center ${
                        selectedAvatar === avatar.url
                          ? "bg-black/30"
                          : "bg-black/50 opacity-0 group-hover:opacity-100"
                      } transition-all duration-200`}>
                        {selectedAvatar === avatar.url ? (
                          <div className="bg-white rounded-full p-1">
                            <Check size={16} className="text-black" />
                          </div>
                        ) : (
                          <div className="bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-xs text-white">
                            Select
                          </div>
                        )}
                      </div>
                    </button>

                    {/* Selection indicator below image */}
                    {selectedAvatar === avatar.url && (
                      <div className="mt-2 flex items-center justify-center">
                        <div className="bg-white/10 rounded-full px-2 py-0.5 text-[10px] text-white/80 flex items-center gap-1">
                          <span>Selected</span>
                          <Check size={10} className="text-white" />
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-white/5 flex justify-between items-center">
              <button
                onClick={onClose}
                className="text-white/50 hover:text-white text-sm transition-all duration-200"
              >
                Cancel
              </button>

              {selectedAvatar && (
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="flex items-center gap-2 text-sm text-white bg-white/10 hover:bg-white/15 px-4 py-2 rounded-md transition-all duration-200"
                >
                  {isSaving ? "Saving..." : "Apply Selection"}
                  <ChevronRight size={16} />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvatarSelectionModal;
