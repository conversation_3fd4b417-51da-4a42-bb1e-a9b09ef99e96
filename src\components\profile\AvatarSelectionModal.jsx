import { useState, useEffect } from "react";
import { X, Check, User, ChevronRight } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Avatar categories with their images
// These will be replaced with your CDN links
const AVATAR_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    avatars: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    avatars: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    avatars: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const AvatarSelectionModal = ({ isOpen, onClose, onSelect, currentAvatar }) => {
  const { user } = useAniList();
  const [selectedAvatar, setSelectedAvatar] = useState(null);
  const [activeCategory, setActiveCategory] = useState(AVATAR_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);

  // Set the current avatar as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentAvatar) {
      setSelectedAvatar(currentAvatar);
    }
  }, [isOpen, currentAvatar]);

  // Handle avatar selection
  const handleAvatarSelect = (avatarUrl) => {
    setSelectedAvatar(avatarUrl);
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedAvatar) return;

    setIsSaving(true);
    try {
      // Save the selected avatar to the user's profile
      await profileApi.updateProfilePicture(user.id, selectedAvatar);

      // Call the onSelect callback with the selected avatar
      onSelect(selectedAvatar);

      toast.success("Profile picture updated successfully!");
      onClose();
    } catch (error) {
      console.error("Error updating profile picture:", error);
      toast.error("Failed to update profile picture. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/95">
      {/* Fixed-size modal - not responsive */}
      <div className="w-[900px] h-[600px] overflow-hidden rounded-none border border-white/10 shadow-[0_0_40px_rgba(0,0,0,0.9)] bg-black">
        {/* Glass panel effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none"></div>

        {/* Top bar with title and close button */}
        <div className="h-12 bg-black flex items-center justify-between px-6 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 rounded-full bg-white"></div>
            <h2 className="text-sm font-bold text-white tracking-[0.2em]">SELECT AVATAR</h2>
          </div>
          <button
            onClick={onClose}
            className="text-white/60 hover:text-white transition-all duration-200"
          >
            <X size={18} />
          </button>
        </div>

        <div className="flex h-[calc(600px-48px)]">
          {/* Left panel - Preview */}
          <div className="w-[300px] border-r border-white/10 flex flex-col">
            {/* Preview header */}
            <div className="h-10 border-b border-white/10 flex items-center px-6">
              <span className="text-xs text-white/50 uppercase tracking-wider">Preview</span>
            </div>

            {/* Preview content */}
            <div className="flex-1 flex flex-col items-center justify-center p-8 relative">
              {/* Background pattern */}
              <div className="absolute inset-0 pointer-events-none">
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_white_0%,_transparent_70%)] opacity-10"></div>
              </div>

              {/* Avatar preview */}
              <div className="mb-8">
                {selectedAvatar ? (
                  <div className="relative">
                    <div className="w-[160px] h-[160px] rounded-full overflow-hidden border-2 border-white/20 shadow-[0_0_30px_rgba(0,0,0,0.7)]">
                      <img
                        src={selectedAvatar}
                        alt="Selected avatar"
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                    </div>
                    <div className="absolute -bottom-2 -right-2 bg-white rounded-full p-1.5 shadow-[0_0_10px_rgba(0,0,0,0.5)]">
                      <Check size={14} className="text-black" />
                    </div>
                  </div>
                ) : (
                  <div className="w-[160px] h-[160px] rounded-full flex items-center justify-center bg-gradient-to-br from-white/5 to-transparent border-2 border-dashed border-white/10">
                    <User size={50} className="text-white/20" />
                  </div>
                )}
              </div>

              {/* Status text */}
              <div className="text-center mb-8">
                <p className="text-white/70 text-sm">
                  {selectedAvatar ? "Avatar Selected" : "No Avatar Selected"}
                </p>
                <p className="text-white/40 text-xs mt-1">
                  {selectedAvatar ? "Click Apply to save your selection" : "Choose from the gallery"}
                </p>
              </div>

              {/* Action button */}
              {selectedAvatar && (
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className={`w-full py-2.5 rounded-sm bg-white text-black text-sm font-medium transition-all duration-200 hover:bg-white/90 ${
                    isSaving ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                >
                  {isSaving ? "Saving..." : "APPLY"}
                </button>
              )}
            </div>
          </div>

          {/* Right panel - Gallery */}
          <div className="w-[600px] flex flex-col">
            {/* Category tabs */}
            <div className="h-10 border-b border-white/10 flex items-center px-2">
              {AVATAR_CATEGORIES.map(category => (
                <button
                  key={category.id}
                  className={`px-4 h-10 text-xs font-medium transition-all duration-200 ${
                    activeCategory === category.id
                      ? "bg-white/10 text-white"
                      : "text-white/50 hover:text-white/80 hover:bg-white/5"
                  }`}
                  onClick={() => setActiveCategory(category.id)}
                >
                  {category.name.toUpperCase()}
                </button>
              ))}
            </div>

            {/* Gallery */}
            <div className="flex-1 p-6 overflow-y-auto scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
              <div className="grid grid-cols-5 gap-4">
                {AVATAR_CATEGORIES.find(c => c.id === activeCategory)?.avatars.map(avatar => (
                  <div
                    key={avatar.id}
                    className="group"
                  >
                    <button
                      className={`relative w-full aspect-square transition-all duration-200 ${
                        selectedAvatar === avatar.url
                          ? "ring-2 ring-white"
                          : "ring-1 ring-white/10 hover:ring-white/30"
                      }`}
                      onClick={() => handleAvatarSelect(avatar.url)}
                    >
                      <img
                        src={avatar.url}
                        alt={`Avatar ${avatar.id}`}
                        className="w-full h-full object-cover"
                      />
                      <div className={`absolute inset-0 flex items-center justify-center ${
                        selectedAvatar === avatar.url
                          ? "bg-black/30"
                          : "bg-black/70 opacity-0 group-hover:opacity-100"
                      } transition-all duration-200`}>
                        {selectedAvatar === avatar.url ? (
                          <Check size={20} className="text-white" />
                        ) : (
                          <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center">
                            <Check size={16} className="text-white" />
                          </div>
                        )}
                      </div>
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Footer */}
            <div className="h-12 border-t border-white/10 flex items-center justify-between px-6">
              <div className="text-xs text-white/40">
                {selectedAvatar ? "1 avatar selected" : "No selection"}
              </div>

              <div className="flex items-center gap-4">
                <button
                  onClick={onClose}
                  className="text-white/50 hover:text-white text-xs transition-all duration-200"
                >
                  CANCEL
                </button>

                <button
                  onClick={handleSave}
                  disabled={!selectedAvatar || isSaving}
                  className={`px-4 py-1.5 text-xs font-medium transition-all duration-200 ${
                    !selectedAvatar || isSaving
                      ? "bg-white/10 text-white/30 cursor-not-allowed"
                      : "bg-white text-black hover:bg-white/90"
                  }`}
                >
                  {isSaving ? "SAVING..." : "APPLY SELECTION"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvatarSelectionModal;
