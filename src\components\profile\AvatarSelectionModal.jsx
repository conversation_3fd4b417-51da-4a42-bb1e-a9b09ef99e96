import { useState, useEffect, useRef } from "react";
import { X, Check, User, ChevronDown } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Avatar categories with their images
// These will be replaced with your CDN links
const AVATAR_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    avatars: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr6", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr7", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr8", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr9", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr10", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr11", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr12", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr13", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr14", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr15", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr16", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr17", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr18", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr19", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr20", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    avatars: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot6", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot7", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot8", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot9", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot10", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot11", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot12", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot13", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot14", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot15", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot16", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    avatars: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv6", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv7", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv8", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv9", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv10", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv11", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv12", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const AvatarSelectionModal = ({ isOpen, onClose, onSelect, currentAvatar }) => {
  const { user } = useAniList();
  const [selectedAvatar, setSelectedAvatar] = useState(null);
  const [activeCategory, setActiveCategory] = useState(AVATAR_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Touch handling for swipe to close
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const bottomSheetRef = useRef(null);

  // Set the current avatar as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentAvatar) {
      setSelectedAvatar(currentAvatar);
    }
    if (isOpen) {
      setIsAnimating(true);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, currentAvatar]);

  // Handle avatar selection
  const handleAvatarSelect = (avatarUrl) => {
    setSelectedAvatar(avatarUrl);
  };

  // Handle close with animation
  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300); // Match animation duration
  };

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Touch handlers for swipe to close
  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientY);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isDownSwipe = distance < -50; // Swipe down threshold

    if (isDownSwipe) {
      handleClose();
    }
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedAvatar) return;

    setIsSaving(true);
    try {
      // Save the selected avatar to the user's profile
      await profileApi.updateProfilePicture(user.id, selectedAvatar);

      // Call the onSelect callback with the selected avatar
      onSelect(selectedAvatar);

      toast.success("Profile picture updated successfully!");
      handleClose();
    } catch (error) {
      console.error("Error updating profile picture:", error);
      toast.error("Failed to update profile picture. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 z-50 transition-all duration-300 ${
        isAnimating ? 'bg-black/80' : 'bg-black/0'
      }`}
      onClick={handleBackdropClick}
    >
      {/* Bottom Sheet */}
      <div
        ref={bottomSheetRef}
        className={`fixed bottom-0 left-0 right-0 mx-auto max-w-md bg-black/90 backdrop-blur-xl border border-white/10 border-b-0 shadow-[0_-10px_40px_rgba(0,0,0,0.8)] transition-all duration-300 ease-out ${
          isAnimating ? 'translate-y-0' : 'translate-y-full'
        }`}
        style={{
          borderTopLeftRadius: '24px',
          borderTopRightRadius: '24px',
          height: '70vh',
          maxHeight: '600px'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Glass panel effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-white/5 to-transparent pointer-events-none rounded-t-3xl"></div>

        {/* Drag handle */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-10 h-1 bg-white/30 rounded-full"></div>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between px-6 py-3 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 rounded-full bg-white"></div>
            <h2 className="text-lg font-bold text-white">Choose Avatar</h2>
          </div>
          <button
            onClick={handleClose}
            className="p-2 rounded-full text-white/60 hover:text-white hover:bg-white/10 transition-all duration-200"
          >
            <X size={20} />
          </button>
        </div>

        {/* Preview section */}
        <div className="px-6 py-4 border-b border-white/10">
          <div className="flex items-center gap-4">
            {/* Avatar preview */}
            <div className="relative">
              {selectedAvatar ? (
                <div className="relative">
                  <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-white/20 shadow-lg">
                    <img
                      src={selectedAvatar}
                      alt="Selected avatar"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-1">
                    <Check size={12} className="text-black" />
                  </div>
                </div>
              ) : (
                <div className="w-16 h-16 rounded-full flex items-center justify-center bg-white/5 border-2 border-dashed border-white/10">
                  <User size={24} className="text-white/30" />
                </div>
              )}
            </div>

            {/* Status text */}
            <div className="flex-1">
              <p className="text-white font-medium">
                {selectedAvatar ? "Avatar Selected" : "Select an Avatar"}
              </p>
              <p className="text-white/60 text-sm">
                {selectedAvatar ? "Tap Apply to save changes" : "Choose from the options below"}
              </p>
            </div>
          </div>
        </div>

        {/* Category tabs */}
        <div className="border-b border-white/10 overflow-x-auto scrollbar-none">
          <div className="flex px-6">
            {AVATAR_CATEGORIES.map(category => (
              <button
                key={category.id}
                className={`px-4 py-3 text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                  activeCategory === category.id
                    ? "text-white border-b-2 border-white"
                    : "text-white/60 hover:text-white"
                }`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Avatar gallery */}
        <div className="flex-1 min-h-0">
          <div
            className="h-full overflow-y-auto px-4 pb-28"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: 'rgba(255,255,255,0.2) transparent'
            }}
          >
            <div className="grid grid-cols-4 gap-3 py-4">
              {AVATAR_CATEGORIES.find(c => c.id === activeCategory)?.avatars.map(avatar => (
                <button
                  key={avatar.id}
                  className={`group relative aspect-square rounded-2xl overflow-hidden transition-all duration-300 ${
                    selectedAvatar === avatar.url
                      ? "ring-2 ring-cyan-400 shadow-lg shadow-cyan-400/25 scale-105"
                      : "ring-1 ring-white/20 hover:ring-cyan-400/50 hover:scale-105 hover:shadow-lg hover:shadow-white/10"
                  }`}
                  onClick={() => handleAvatarSelect(avatar.url)}
                >
                  <img
                    src={avatar.url}
                    alt={`Avatar ${avatar.id}`}
                    className="w-full h-full object-cover"
                  />

                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  {/* Selection indicator */}
                  <div className={`absolute inset-0 flex items-center justify-center ${
                    selectedAvatar === avatar.url
                      ? "bg-gradient-to-t from-cyan-500/30 via-transparent to-transparent"
                      : ""
                  } transition-all duration-300`}>
                    {selectedAvatar === avatar.url && (
                      <div className="bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full p-2 shadow-lg animate-pulse">
                        <Check size={16} className="text-white" />
                      </div>
                    )}
                  </div>

                  {/* Hover effect */}
                  <div className="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Fixed Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black via-black/95 to-transparent backdrop-blur-xl border-t border-white/10">
          <div className="flex gap-3">
            <button
              onClick={handleClose}
              className="flex-1 py-4 px-6 rounded-2xl bg-gradient-to-r from-gray-800/80 to-gray-700/80 hover:from-gray-700/80 hover:to-gray-600/80 text-white font-semibold transition-all duration-300 border border-white/10 hover:border-white/20 backdrop-blur-sm"
            >
              Cancel
            </button>

            <button
              onClick={handleSave}
              disabled={!selectedAvatar || isSaving}
              className={`flex-1 py-4 px-6 rounded-2xl font-semibold transition-all duration-300 ${
                !selectedAvatar || isSaving
                  ? "bg-gradient-to-r from-gray-600/50 to-gray-500/50 text-white/50 cursor-not-allowed border border-white/5"
                  : "bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white shadow-lg shadow-cyan-500/25 hover:shadow-cyan-500/40 border border-cyan-400/20 hover:scale-105"
              }`}
            >
              {isSaving ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Saving...
                </div>
              ) : (
                "Apply"
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvatarSelectionModal;
