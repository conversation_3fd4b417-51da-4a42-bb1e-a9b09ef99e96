import { useState, useEffect, useRef } from "react";
import { X, Check, User, ChevronDown } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Avatar categories with their images
// These will be replaced with your CDN links
const AVATAR_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    avatars: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    avatars: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    avatars: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const AvatarSelectionModal = ({ isOpen, onClose, onSelect, currentAvatar }) => {
  const { user } = useAniList();
  const [selectedAvatar, setSelectedAvatar] = useState(null);
  const [activeCategory, setActiveCategory] = useState(AVATAR_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Touch handling for swipe to close
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const bottomSheetRef = useRef(null);

  // Set the current avatar as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentAvatar) {
      setSelectedAvatar(currentAvatar);
    }
    if (isOpen) {
      setIsAnimating(true);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, currentAvatar]);

  // Handle avatar selection
  const handleAvatarSelect = (avatarUrl) => {
    setSelectedAvatar(avatarUrl);
  };

  // Handle close with animation
  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300); // Match animation duration
  };

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Touch handlers for swipe to close
  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientY);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isDownSwipe = distance < -50; // Swipe down threshold

    if (isDownSwipe) {
      handleClose();
    }
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedAvatar) return;

    setIsSaving(true);
    try {
      // Save the selected avatar to the user's profile
      await profileApi.updateProfilePicture(user.id, selectedAvatar);

      // Call the onSelect callback with the selected avatar
      onSelect(selectedAvatar);

      toast.success("Profile picture updated successfully!");
      handleClose();
    } catch (error) {
      console.error("Error updating profile picture:", error);
      toast.error("Failed to update profile picture. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 z-50 transition-all duration-300 ${
        isAnimating ? 'bg-black/80' : 'bg-black/0'
      }`}
      onClick={handleBackdropClick}
    >
      {/* Bottom Sheet */}
      <div
        ref={bottomSheetRef}
        className={`fixed bottom-0 left-0 right-0 mx-auto max-w-md bg-black/90 backdrop-blur-xl border border-white/10 border-b-0 shadow-[0_-10px_40px_rgba(0,0,0,0.8)] transition-all duration-300 ease-out ${
          isAnimating ? 'translate-y-0' : 'translate-y-full'
        }`}
        style={{
          borderTopLeftRadius: '24px',
          borderTopRightRadius: '24px',
          height: '70vh',
          maxHeight: '600px'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Glass panel effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-white/5 to-transparent pointer-events-none rounded-t-3xl"></div>

        {/* Drag handle */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-10 h-1 bg-white/30 rounded-full"></div>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between px-6 py-3 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 rounded-full bg-white"></div>
            <h2 className="text-lg font-bold text-white">Choose Avatar</h2>
          </div>
          <button
            onClick={handleClose}
            className="p-2 rounded-full text-white/60 hover:text-white hover:bg-white/10 transition-all duration-200"
          >
            <X size={20} />
          </button>
        </div>

        {/* Preview section */}
        <div className="px-6 py-4 border-b border-white/10">
          <div className="flex items-center gap-4">
            {/* Avatar preview */}
            <div className="relative">
              {selectedAvatar ? (
                <div className="relative">
                  <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-white/20 shadow-lg">
                    <img
                      src={selectedAvatar}
                      alt="Selected avatar"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-1">
                    <Check size={12} className="text-black" />
                  </div>
                </div>
              ) : (
                <div className="w-16 h-16 rounded-full flex items-center justify-center bg-white/5 border-2 border-dashed border-white/10">
                  <User size={24} className="text-white/30" />
                </div>
              )}
            </div>

            {/* Status text */}
            <div className="flex-1">
              <p className="text-white font-medium">
                {selectedAvatar ? "Avatar Selected" : "Select an Avatar"}
              </p>
              <p className="text-white/60 text-sm">
                {selectedAvatar ? "Tap Apply to save changes" : "Choose from the options below"}
              </p>
            </div>
          </div>
        </div>

        {/* Category tabs */}
        <div className="border-b border-white/10 overflow-x-auto scrollbar-none">
          <div className="flex px-6">
            {AVATAR_CATEGORIES.map(category => (
              <button
                key={category.id}
                className={`px-4 py-3 text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                  activeCategory === category.id
                    ? "text-white border-b-2 border-white"
                    : "text-white/60 hover:text-white"
                }`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Avatar gallery */}
        <div className="flex-1 p-6 overflow-y-auto scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
          <div className="grid grid-cols-4 gap-4">
            {AVATAR_CATEGORIES.find(c => c.id === activeCategory)?.avatars.map(avatar => (
              <button
                key={avatar.id}
                className={`relative aspect-square rounded-xl overflow-hidden transition-all duration-200 ${
                  selectedAvatar === avatar.url
                    ? "ring-2 ring-white scale-105"
                    : "ring-1 ring-white/10 hover:ring-white/30 hover:scale-105"
                }`}
                onClick={() => handleAvatarSelect(avatar.url)}
              >
                <img
                  src={avatar.url}
                  alt={`Avatar ${avatar.id}`}
                  className="w-full h-full object-cover"
                />
                <div className={`absolute inset-0 flex items-center justify-center ${
                  selectedAvatar === avatar.url
                    ? "bg-black/30"
                    : "bg-black/60 opacity-0 hover:opacity-100"
                } transition-all duration-200`}>
                  {selectedAvatar === avatar.url && (
                    <div className="bg-white rounded-full p-1">
                      <Check size={16} className="text-black" />
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10 bg-black/50">
          <div className="flex gap-3">
            <button
              onClick={handleClose}
              className="flex-1 py-3 px-4 rounded-xl bg-white/10 hover:bg-white/20 text-white font-medium transition-all duration-200"
            >
              Cancel
            </button>

            <button
              onClick={handleSave}
              disabled={!selectedAvatar || isSaving}
              className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-200 ${
                !selectedAvatar || isSaving
                  ? "bg-white/20 text-white/50 cursor-not-allowed"
                  : "bg-white text-black hover:bg-white/90"
              }`}
            >
              {isSaving ? "Saving..." : "Apply"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvatarSelectionModal;
