import { useState, useEffect } from "react";
import { X, Check } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import { profileApi } from "@/api/profileApi";
import { toast } from "sonner";

// Avatar categories with their images
// These will be replaced with your CDN links
const AVATAR_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    avatars: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    avatars: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    avatars: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const AvatarSelectionModal = ({ isOpen, onClose, onSelect, currentAvatar }) => {
  const { user } = useAniList();
  const [selectedAvatar, setSelectedAvatar] = useState(null);
  const [activeCategory, setActiveCategory] = useState(AVATAR_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);

  // Set the current avatar as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentAvatar) {
      setSelectedAvatar(currentAvatar);
    }
  }, [isOpen, currentAvatar]);

  // Handle avatar selection
  const handleAvatarSelect = (avatarUrl) => {
    setSelectedAvatar(avatarUrl);
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedAvatar) return;
    
    setIsSaving(true);
    try {
      // Save the selected avatar to the user's profile
      await profileApi.updateProfilePicture(user.id, selectedAvatar);
      
      // Call the onSelect callback with the selected avatar
      onSelect(selectedAvatar);
      
      toast.success("Profile picture updated successfully!");
      onClose();
    } catch (error) {
      console.error("Error updating profile picture:", error);
      toast.error("Failed to update profile picture. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80">
      <div className="bg-gray-900 rounded-lg w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-white/10 flex justify-between items-center">
          <h2 className="text-xl font-bold">Avatar Selection</h2>
          <button 
            onClick={onClose}
            className="p-1 rounded-full hover:bg-white/10"
          >
            <X size={20} />
          </button>
        </div>
        
        <div className="p-4">
          <p className="text-white/60 mb-4">Choose your avatar! You can change it at any time.</p>
          
          {/* Preview of selected avatar */}
          {selectedAvatar && (
            <div className="flex justify-center mb-6">
              <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-white/10">
                <img 
                  src={selectedAvatar} 
                  alt="Selected avatar" 
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          )}
          
          {/* Category tabs */}
          <div className="flex border-b border-white/10 mb-4">
            {AVATAR_CATEGORIES.map(category => (
              <button
                key={category.id}
                className={`px-4 py-2 text-sm font-medium ${
                  activeCategory === category.id
                    ? "text-white border-b-2 border-blue-500"
                    : "text-white/60 hover:text-white/80"
                }`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
          
          {/* Avatar grid */}
          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4 overflow-y-auto max-h-[40vh] p-2">
            {AVATAR_CATEGORIES.find(c => c.id === activeCategory)?.avatars.map(avatar => (
              <button
                key={avatar.id}
                className={`relative rounded-full overflow-hidden border-2 aspect-square ${
                  selectedAvatar === avatar.url
                    ? "border-blue-500"
                    : "border-white/10 hover:border-white/30"
                }`}
                onClick={() => handleAvatarSelect(avatar.url)}
              >
                <img 
                  src={avatar.url} 
                  alt={`Avatar ${avatar.id}`} 
                  className="w-full h-full object-cover"
                />
                {selectedAvatar === avatar.url && (
                  <div className="absolute inset-0 bg-blue-500/30 flex items-center justify-center">
                    <Check size={24} className="text-white" />
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
        
        {/* Footer with action buttons */}
        <div className="p-4 border-t border-white/10 flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded bg-white/10 hover:bg-white/20 text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!selectedAvatar || isSaving}
            className={`px-4 py-2 rounded bg-blue-600 hover:bg-blue-700 text-sm font-medium ${
              !selectedAvatar || isSaving ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {isSaving ? "Saving..." : "Save"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AvatarSelectionModal;
