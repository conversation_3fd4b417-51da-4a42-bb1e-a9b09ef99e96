import UserProfile from '../models/UserProfile.js';

// Helper functions for achievements and ranks
async function checkStreakAchievements(userProfile, streak) {
  // Define streak milestones
  const streakMilestones = [3, 7, 14, 30, 60, 100, 365];

  // Check if current streak hits a milestone
  const milestone = streakMilestones.find(m => streak === m);

  if (milestone) {
    // Calculate XP reward (higher for longer streaks)
    const xpReward = streak * 5;

    // Create achievement
    const newAchievement = {
      id: `streak-${streak}`,
      title: `${streak} Day Streak!`,
      description: `You've visited the site for ${streak} consecutive days!`,
      date: new Date(),
      type: 'streak'
    };

    // Create reward
    const newReward = {
      id: `streak-reward-${streak}`,
      title: `${streak} Day Streak Reward`,
      description: `+${xpReward} XP`,
      date: new Date(),
      value: xpReward,
      type: 'xp'
    };

    // Add achievement and reward if they don't already exist
    if (!userProfile.achievements.some(a => a.id === newAchievement.id)) {
      userProfile.achievements.push(newAchievement);
      userProfile.rewards.push(newReward);

      // Add XP
      userProfile.xp += xpReward;

      // Update level based on new XP
      updateLevel(userProfile);

      return true;
    }
  }

  return false;
}

async function checkLikeMilestones(userProfile, likes) {
  // Define like milestones
  const likeMilestones = [10, 50, 100, 500, 1000];

  // Check if current likes hits a milestone
  const milestone = likeMilestones.find(m => likes === m);

  if (milestone) {
    // Calculate XP reward
    const xpReward = milestone;

    // Create achievement
    const newAchievement = {
      id: `likes-${milestone}`,
      title: `${milestone} Likes!`,
      description: `Your profile has received ${milestone} likes!`,
      date: new Date(),
      type: 'likes'
    };

    // Create reward
    const newReward = {
      id: `likes-reward-${milestone}`,
      title: `${milestone} Likes Reward`,
      description: `+${xpReward} XP`,
      date: new Date(),
      value: xpReward,
      type: 'xp'
    };

    // Add achievement and reward if they don't already exist
    if (!userProfile.achievements.some(a => a.id === newAchievement.id)) {
      userProfile.achievements.push(newAchievement);
      userProfile.rewards.push(newReward);

      // Add XP
      userProfile.xp += xpReward;

      // Update level based on new XP
      updateLevel(userProfile);

      return true;
    }
  }

  return false;
}

function updateLevel(userProfile) {
  // Calculate level based on XP (more gradual progression)
  // Formula: level = (xp/100)^0.4 * 1.5
  const newLevel = Math.max(1, Math.floor(Math.pow(userProfile.xp / 100, 0.4) * 1.5));

  // Check if user leveled up
  if (newLevel > userProfile.level) {
    // Add level-up bonus XP
    const bonusXp = newLevel * 10;
    userProfile.xp += bonusXp;

    // Create level-up achievement
    const levelAchievement = {
      id: `level-${newLevel}`,
      title: `Reached Level ${newLevel}!`,
      description: `You've reached level ${newLevel} on your anime journey!`,
      date: new Date(),
      type: 'level'
    };

    const levelReward = {
      id: `level-reward-${newLevel}`,
      title: `Level ${newLevel} Reward`,
      description: `+${bonusXp} XP`,
      date: new Date(),
      value: bonusXp,
      type: 'xp'
    };

    // Add achievement and reward if they don't already exist
    if (!userProfile.achievements.some(a => a.id === levelAchievement.id)) {
      userProfile.achievements.push(levelAchievement);
      userProfile.rewards.push(levelReward);
    }

    // Update level
    userProfile.level = newLevel;
  }
}

// Controller methods
const userProfileController = {
  // Get leaderboard
  getLeaderboard: async (req, res) => {
    try {
      const { type = 'xp', limit = 10 } = req.query;

      let sortField;
      switch (type) {
        case 'streak':
          sortField = { streak: -1 };
          break;
        case 'longestStreak':
          sortField = { longestStreak: -1 };
          break;
        case 'likes':
          sortField = { likes: -1 };
          break;
        case 'xp':
        default:
          sortField = { xp: -1 };
          break;
      }

      // Get top users based on the specified field
      const leaderboard = await UserProfile.find({})
        .sort(sortField)
        .limit(parseInt(limit))
        .select('anilistId level xp streak longestStreak likes rank');

      res.status(200).json(leaderboard);
    } catch (error) {
      console.error('Error getting leaderboard:', error);
      res.status(500).json({ message: error.message });
    }
  },
  // Get user profile
  getUserProfile: async (req, res) => {
    try {
      const { userId } = req.params;

      console.log(`Getting profile for user: ${userId}`);

      // Find user profile or create if it doesn't exist
      let userProfile = await UserProfile.findOne({ anilistId: userId });

      if (!userProfile) {
        console.log(`Creating new profile for user: ${userId}`);

        // Initialize with default values
        userProfile = new UserProfile({
          anilistId: userId,
          level: 1,
          xp: 0,
          rank: 'Rookie',
          streak: 1,
          longestStreak: 1,
          lastVisit: new Date(),
          visitDates: [new Date()],
          likes: 0,
          achievements: [],
          rewards: [],
          tasks: {
            completedTasks: [],
            lastReset: new Date()
          }
        });

        try {
          await userProfile.save();
          console.log(`New profile saved for user: ${userId}`);
        } catch (saveError) {
          console.error(`Error saving new profile for user ${userId}:`, saveError);
          // If save fails, still return the created profile object
        }
      } else {
        console.log(`Found existing profile for user: ${userId}`);
      }

      // Return the profile data
      res.status(200).json(userProfile);
    } catch (error) {
      console.error('Error getting user profile:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // Update user profile
  updateUserProfile: async (req, res) => {
    try {
      const { userId } = req.params;
      const updateData = req.body;

      console.log(`Updating profile for user: ${userId}`, updateData);

      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });

      if (!userProfile) {
        console.log(`Creating new profile during update for user: ${userId}`);

        // Initialize with default values and update data
        userProfile = new UserProfile({
          anilistId: userId,
          level: updateData.level || 1,
          xp: updateData.xp || 0,
          rank: updateData.rank || 'Rookie',
          streak: updateData.streak || 1,
          longestStreak: updateData.longestStreak || 1,
          lastVisit: updateData.lastVisit || new Date(),
          visitDates: updateData.visitDates || [new Date()],
          likes: updateData.likes || 0,
          achievements: updateData.achievements || [],
          rewards: updateData.rewards || [],
          tasks: updateData.tasks || {
            completedTasks: [],
            lastReset: new Date()
          }
        });
      } else {
        console.log(`Updating existing profile for user: ${userId}`);

        // Update fields carefully
        Object.keys(updateData).forEach(key => {
          // Special handling for nested objects
          if (key === 'tasks' && updateData.tasks) {
            // Ensure tasks object exists
            if (!userProfile.tasks) {
              userProfile.tasks = { completedTasks: [], lastReset: new Date() };
            }

            // Update completedTasks if provided
            if (updateData.tasks.completedTasks) {
              userProfile.tasks.completedTasks = updateData.tasks.completedTasks;
            }

            // Update lastReset if provided
            if (updateData.tasks.lastReset) {
              userProfile.tasks.lastReset = updateData.tasks.lastReset;
            }
          } else {
            // For regular fields, just update directly
            userProfile[key] = updateData[key];
          }
        });
      }

      try {
        await userProfile.save();
        console.log(`Profile saved for user: ${userId}`);
      } catch (saveError) {
        console.error(`Error saving profile for user ${userId}:`, saveError);
        // If save fails, still return the updated profile object
      }

      res.status(200).json(userProfile);
    } catch (error) {
      console.error('Error updating user profile:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // Record a visit and update streak
  recordVisit: async (req, res) => {
    try {
      const { userId } = req.params;

      console.log(`Recording visit for user ${userId}`);

      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });

      if (!userProfile) {
        console.log(`Creating new profile for user ${userId}`);
        userProfile = new UserProfile({
          anilistId: userId,
          level: 1,
          xp: 0,
          rank: 'Rookie',
          streak: 1,
          longestStreak: 1,
          visitDates: [],
          likes: 0,
          tasks: {
            completedTasks: [],
            lastReset: new Date().setHours(0, 0, 0, 0)
          }
        });

        // Save the new profile first
        try {
          await userProfile.save();
          console.log(`New profile created and saved for user ${userId}`);
        } catch (createError) {
          console.error(`Error creating new profile for user ${userId}:`, createError);
          return res.status(500).json({ message: 'Error creating user profile' });
        }
      }

      // Log the current state of the profile
      console.log(`Current profile state for user ${userId}:`, {
        streak: userProfile.streak,
        longestStreak: userProfile.longestStreak,
        lastVisit: userProfile.lastVisit,
        visitDates: userProfile.visitDates ? userProfile.visitDates.length : 0
      });

      // Get today's date (reset to midnight)
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTimestamp = today.getTime();

      // Get yesterday's date
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayTimestamp = yesterday.getTime();

      // Check if we already recorded a visit today
      let lastVisitDate = null;
      if (userProfile.lastVisit) {
        lastVisitDate = new Date(userProfile.lastVisit);
        lastVisitDate.setHours(0, 0, 0, 0);
      }

      console.log(`Last visit date: ${lastVisitDate}, Today: ${today}`);

      if (lastVisitDate && lastVisitDate.getTime() === todayTimestamp) {
        console.log(`Already recorded a visit today for user ${userId}`);
        return res.status(200).json({
          message: 'Already recorded a visit today',
          streak: userProfile.streak,
          longestStreak: userProfile.longestStreak,
          xp: userProfile.xp,
          level: userProfile.level
        });
      }

      // Initialize visit dates array if it doesn't exist
      if (!userProfile.visitDates) {
        userProfile.visitDates = [];
      }

      // Check if today is already in visitDates
      const hasVisitedToday = userProfile.visitDates.some(date => {
        const visitDate = new Date(date);
        visitDate.setHours(0, 0, 0, 0);
        return visitDate.getTime() === todayTimestamp;
      });

      if (hasVisitedToday) {
        console.log(`Today already in visitDates for user ${userId}`);
      } else {
        // Add today to visit dates
        console.log(`Adding today to visitDates for user ${userId}`);
        userProfile.visitDates.push(today);
      }

      // Update streak logic
      let newStreak = userProfile.streak || 1;
      let visitedYesterday = false;

      if (lastVisitDate) {
        visitedYesterday = lastVisitDate.getTime() === yesterdayTimestamp;
      } else {
        // Check if yesterday is in visitDates
        visitedYesterday = userProfile.visitDates.some(date => {
          const visitDate = new Date(date);
          visitDate.setHours(0, 0, 0, 0);
          return visitDate.getTime() === yesterdayTimestamp;
        });
      }

      if (visitedYesterday) {
        // Consecutive day, increment streak
        newStreak += 1;
        console.log(`Streak increased to ${newStreak} days for user ${userId}`);
      } else if (!hasVisitedToday) {
        // Not consecutive and not today already, reset streak
        newStreak = 1;
        console.log(`Streak reset to 1 day for user ${userId}`);
      }

      // For demonstration purposes, let's set a minimum streak of 1
      newStreak = Math.max(1, newStreak);

      // Update longest streak if current streak is longer
      const newLongestStreak = Math.max(userProfile.longestStreak || 1, newStreak);

      console.log(`New streak: ${newStreak}, New longest streak: ${newLongestStreak} for user ${userId}`);

      // Add XP for daily visit (10 XP) only if we haven't already recorded a visit today
      const addXpForVisit = !hasVisitedToday;
      const xpToAdd = addXpForVisit ? 10 : 0;

      if (addXpForVisit) {
        console.log(`Adding 10 XP for daily visit for user ${userId}`);
        userProfile.xp = (userProfile.xp || 0) + xpToAdd;

        // Update level based on new XP
        updateLevel(userProfile);
      } else {
        console.log(`Not adding XP since visit was already recorded for user ${userId}`);
      }

      // Use findOneAndUpdate to ensure atomic update
      try {
        const updatedProfile = await UserProfile.findOneAndUpdate(
          { anilistId: userId },
          {
            $set: {
              streak: newStreak,
              longestStreak: newLongestStreak,
              lastVisit: today,
              xp: userProfile.xp,
              level: userProfile.level
            },
            $addToSet: {
              visitDates: today
            }
          },
          { new: true, runValidators: true }
        );

        if (!updatedProfile) {
          throw new Error('Profile not found during update');
        }

        console.log(`User profile updated successfully for ${userId} using findOneAndUpdate`);

        // Check for streak achievements after successful update
        if (addXpForVisit) {
          await checkStreakAchievements(updatedProfile, newStreak);
        }

        // Use the updated profile for the response
        userProfile = updatedProfile;
      } catch (updateError) {
        console.error(`Error updating user profile for ${userId} with findOneAndUpdate:`, updateError);

        // Fallback to traditional save method
        try {
          // Update the profile with our changes
          userProfile.streak = newStreak;
          userProfile.longestStreak = newLongestStreak;
          userProfile.lastVisit = today;

          await userProfile.save();
          console.log(`User profile saved successfully for ${userId} using traditional save`);

          // Check for streak achievements after successful save
          if (addXpForVisit) {
            await checkStreakAchievements(userProfile, newStreak);
          }

          // Double-check that the save worked by fetching the profile again
          const checkProfile = await UserProfile.findOne({ anilistId: userId });
          console.log(`Profile after save check for ${userId}:`, {
            streak: checkProfile.streak,
            longestStreak: checkProfile.longestStreak,
            lastVisit: checkProfile.lastVisit,
            visitDates: checkProfile.visitDates ? checkProfile.visitDates.length : 0
          });
        } catch (saveError) {
          console.error(`Error saving user profile for ${userId}:`, saveError);

          // Try one more approach - direct update
          try {
            const directUpdate = await UserProfile.updateOne(
              { anilistId: userId },
              {
                $set: {
                  streak: newStreak,
                  longestStreak: newLongestStreak,
                  lastVisit: today,
                  xp: userProfile.xp,
                  level: userProfile.level
                },
                $addToSet: {
                  visitDates: today
                }
              }
            );

            console.log(`Direct update result for ${userId}:`, directUpdate);

            if (directUpdate.modifiedCount === 0) {
              throw new Error('No documents modified during direct update');
            }

            // Fetch the updated profile
            const updatedProfile = await UserProfile.findOne({ anilistId: userId });
            if (!updatedProfile) {
              throw new Error('Profile not found after direct update');
            }

            // Check for streak achievements after successful direct update
            if (addXpForVisit) {
              await checkStreakAchievements(updatedProfile, newStreak);
            }

            // Use the updated profile for the response
            userProfile = updatedProfile;
          } catch (directUpdateError) {
            console.error(`Error with direct update for ${userId}:`, directUpdateError);
            return res.status(500).json({ message: 'Error updating user profile' });
          }
        }
      }

      res.status(200).json({
        message: 'Visit recorded successfully',
        streak: userProfile.streak,
        longestStreak: userProfile.longestStreak,
        xp: userProfile.xp,
        level: userProfile.level,
        visitDates: userProfile.visitDates ? userProfile.visitDates.length : 0
      });
    } catch (error) {
      console.error('Error recording visit:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // Add XP
  addXp: async (req, res) => {
    try {
      const { userId } = req.params;
      const { amount, source } = req.body;

      if (!amount || isNaN(amount)) {
        return res.status(400).json({ message: 'Invalid XP amount' });
      }

      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });

      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId
        });
      }

      // Add XP
      userProfile.xp = (userProfile.xp || 0) + parseInt(amount);

      // Update level based on new XP
      updateLevel(userProfile);

      await userProfile.save();

      res.status(200).json({
        message: 'XP added successfully',
        xp: userProfile.xp,
        level: userProfile.level
      });
    } catch (error) {
      console.error('Error adding XP:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // Add like
  addLike: async (req, res) => {
    try {
      const { userId } = req.params;
      const { likerUserId } = req.body;

      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });

      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId
        });
      }

      // Initialize likedBy array if it doesn't exist
      if (!userProfile.likedBy) {
        userProfile.likedBy = [];
      }

      // Check if this user has already liked the profile
      if (likerUserId && userProfile.likedBy.includes(likerUserId)) {
        return res.status(400).json({ message: 'User has already liked this profile' });
      }

      // Add liker to likedBy array if provided
      if (likerUserId) {
        userProfile.likedBy.push(likerUserId);
      }

      // Increment likes
      userProfile.likes = (userProfile.likes || 0) + 1;

      // Add XP for receiving a like (5 XP)
      userProfile.xp = (userProfile.xp || 0) + 5;

      // Update level based on new XP
      updateLevel(userProfile);

      // Check for like milestones
      await checkLikeMilestones(userProfile, userProfile.likes);

      await userProfile.save();

      // If liker is provided, update their profile too
      if (likerUserId) {
        let likerProfile = await UserProfile.findOne({ anilistId: likerUserId });

        if (likerProfile) {
          // Initialize likedProfiles array if it doesn't exist
          if (!likerProfile.likedProfiles) {
            likerProfile.likedProfiles = [];
          }

          // Add liked profile to likedProfiles array
          if (!likerProfile.likedProfiles.includes(userId)) {
            likerProfile.likedProfiles.push(userId);
            await likerProfile.save();
          }
        }
      }

      res.status(200).json({
        message: 'Like added successfully',
        likes: userProfile.likes
      });
    } catch (error) {
      console.error('Error adding like:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // Get daily tasks
  getDailyTasks: async (req, res) => {
    try {
      const { userId } = req.params;

      console.log(`Getting daily tasks for user ${userId}`);

      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });

      if (!userProfile) {
        console.log(`Creating new profile for user ${userId}`);
        userProfile = new UserProfile({
          anilistId: userId,
          level: 1,
          xp: 0,
          rank: 'Rookie',
          streak: 1,
          longestStreak: 1,
          lastVisit: new Date(),
          visitDates: [new Date()],
          likes: 0,
          tasks: {
            completedTasks: [],
            lastReset: new Date().setHours(0, 0, 0, 0)
          }
        });

        try {
          await userProfile.save();
          console.log(`New profile saved for user ${userId}`);
        } catch (saveError) {
          console.error(`Error saving new profile for user ${userId}:`, saveError);
          // If save fails, still return the created profile object
        }
      }

      console.log(`Updating existing profile for user: ${userId}`);
      console.log(`Current profile state:`, {
        xp: userProfile.xp,
        level: userProfile.level,
        tasks: userProfile.tasks
      });

      // Initialize tasks if it doesn't exist
      if (!userProfile.tasks) {
        console.log(`Initializing tasks for user ${userId}`);
        userProfile.tasks = {
          completedTasks: [],
          lastReset: new Date().setHours(0, 0, 0, 0)
        };

        try {
          await userProfile.save();
          console.log(`Updated profile with initialized tasks for user ${userId}`);
        } catch (saveError) {
          console.error(`Error saving initialized tasks for user ${userId}:`, saveError);
          // If save fails, still continue with the function
        }
      }

      // Ensure completedTasks is an array
      if (!Array.isArray(userProfile.tasks.completedTasks)) {
        console.log(`Fixing completedTasks array for user ${userId}`);
        userProfile.tasks.completedTasks = [];

        try {
          await userProfile.save();
          console.log(`Updated profile with fixed completedTasks for user ${userId}`);
        } catch (saveError) {
          console.error(`Error saving fixed completedTasks for user ${userId}:`, saveError);
          // If save fails, still continue with the function
        }
      }

      // Check if tasks need to be reset (new day)
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTimestamp = today.getTime();

      // Convert lastReset to a timestamp if it's a Date object
      let lastResetTimestamp = userProfile.tasks.lastReset;
      if (userProfile.tasks.lastReset instanceof Date) {
        lastResetTimestamp = userProfile.tasks.lastReset.getTime();
      } else if (typeof userProfile.tasks.lastReset === 'string') {
        lastResetTimestamp = new Date(userProfile.tasks.lastReset).getTime();
      }

      // If lastResetTimestamp is not a valid number, set it to null
      if (isNaN(lastResetTimestamp)) {
        lastResetTimestamp = null;
      }

      console.log(`Last reset: ${lastResetTimestamp}, Today: ${todayTimestamp}`);

      if (!lastResetTimestamp || lastResetTimestamp < todayTimestamp) {
        console.log(`Resetting tasks for new day for user ${userId}`);

        // Reset tasks for new day using findOneAndUpdate for atomic operation
        try {
          const updatedProfile = await UserProfile.findOneAndUpdate(
            { anilistId: userId },
            {
              $set: {
                'tasks.completedTasks': [],
                'tasks.lastReset': today
              }
            },
            { new: true, runValidators: true }
          );

          if (!updatedProfile) {
            throw new Error('Profile not found during update');
          }

          console.log(`Tasks reset successfully for user ${userId} using findOneAndUpdate`);
          userProfile = updatedProfile;
        } catch (updateError) {
          console.error(`Error resetting tasks for user ${userId} with findOneAndUpdate:`, updateError);

          // Fallback to traditional method
          userProfile.tasks = {
            completedTasks: [],
            lastReset: today
          };

          try {
            await userProfile.save();
            console.log(`Updated profile with reset tasks for user ${userId} using traditional save`);
          } catch (saveError) {
            console.error(`Error saving reset tasks for user ${userId}:`, saveError);

            // Try direct update as last resort
            try {
              const directUpdate = await UserProfile.updateOne(
                { anilistId: userId },
                {
                  $set: {
                    'tasks.completedTasks': [],
                    'tasks.lastReset': today
                  }
                }
              );

              console.log(`Direct update result for task reset for ${userId}:`, directUpdate);

              if (directUpdate.modifiedCount === 0) {
                console.warn(`No documents modified during direct update for task reset for ${userId}`);
              }

              // Fetch the updated profile
              const refreshedProfile = await UserProfile.findOne({ anilistId: userId });
              if (refreshedProfile) {
                userProfile = refreshedProfile;
              }
            } catch (directUpdateError) {
              console.error(`Error with direct update for task reset for ${userId}:`, directUpdateError);
            }
          }
        }
      }

      // Ensure we have the latest data
      const finalProfile = await UserProfile.findOne({ anilistId: userId });
      if (finalProfile) {
        userProfile = finalProfile;
      }

      // Save profile to ensure changes are persisted
      try {
        await userProfile.save();
        console.log(`Profile saved for user: ${userId}`);
      } catch (finalSaveError) {
        console.error(`Error saving profile for user ${userId}:`, finalSaveError);
      }

      console.log(`Returning tasks for user ${userId}:`, {
        completedTasks: userProfile.tasks.completedTasks || [],
        lastReset: userProfile.tasks.lastReset
      });

      res.status(200).json({
        completedTasks: userProfile.tasks.completedTasks || [],
        lastReset: userProfile.tasks.lastReset
      });
    } catch (error) {
      console.error('Error getting daily tasks:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // Complete a task
  completeTask: async (req, res) => {
    try {
      const { userId, taskId } = req.params;

      console.log(`Completing task ${taskId} for user ${userId}`);

      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });

      if (!userProfile) {
        console.log(`Creating new profile for user ${userId}`);
        userProfile = new UserProfile({
          anilistId: userId,
          level: 1,
          xp: 0,
          rank: 'Rookie',
          streak: 1,
          longestStreak: 1,
          lastVisit: new Date(),
          visitDates: [new Date()],
          likes: 0,
          tasks: {
            completedTasks: [],
            lastReset: new Date().setHours(0, 0, 0, 0)
          }
        });

        // Save the new profile first
        try {
          await userProfile.save();
          console.log(`New profile created and saved for user ${userId}`);
        } catch (createError) {
          console.error(`Error creating new profile for user ${userId}:`, createError);
          return res.status(500).json({ message: 'Error creating user profile' });
        }
      }

      // Log the current state of the profile
      console.log(`Current profile state for user ${userId}:`, {
        xp: userProfile.xp,
        level: userProfile.level,
        tasks: userProfile.tasks
      });

      // Initialize tasks if it doesn't exist
      if (!userProfile.tasks) {
        console.log(`Initializing tasks for user ${userId}`);
        userProfile.tasks = {
          completedTasks: [],
          lastReset: new Date().setHours(0, 0, 0, 0)
        };

        // Save the profile with initialized tasks
        try {
          await userProfile.save();
          console.log(`Profile updated with initialized tasks for user ${userId}`);
        } catch (initError) {
          console.error(`Error initializing tasks for user ${userId}:`, initError);
          return res.status(500).json({ message: 'Error initializing tasks' });
        }
      }

      // Ensure completedTasks is an array
      if (!Array.isArray(userProfile.tasks.completedTasks)) {
        console.log(`Fixing completedTasks array for user ${userId}`);
        userProfile.tasks.completedTasks = [];

        // Save the profile with fixed completedTasks
        try {
          await userProfile.save();
          console.log(`Profile updated with fixed completedTasks for user ${userId}`);
        } catch (fixError) {
          console.error(`Error fixing completedTasks for user ${userId}:`, fixError);
          return res.status(500).json({ message: 'Error fixing completedTasks' });
        }
      }

      // Check if task is already completed
      if (userProfile.tasks.completedTasks.includes(taskId)) {
        console.log(`Task ${taskId} already completed for user ${userId}`);
        return res.status(200).json({
          message: 'Task already completed',
          completedTasks: userProfile.tasks.completedTasks,
          xpEarned: 0,
          xp: userProfile.xp,
          level: userProfile.level
        });
      }

      // Define task rewards
      const taskRewards = {
        'visit': 10,
        'watch-episode': 15,
        'add-anime': 20,
        'rate-anime': 25,
        'update-progress': 15,
        'explore-genres': 20,
        'watch-seasonal': 30,
        'complete-all': 50
      };

      // Get XP reward for this task
      const xpReward = taskRewards[taskId] || 10;

      console.log(`Adding ${xpReward} XP for completing task ${taskId}`);

      // Add task to completed tasks
      userProfile.tasks.completedTasks.push(taskId);

      console.log(`Updated completed tasks: ${userProfile.tasks.completedTasks}`);

      // Add XP for completing task
      userProfile.xp = (userProfile.xp || 0) + xpReward;

      // Update level based on new XP
      updateLevel(userProfile);

      console.log(`Saving user profile for ${userId} with updated tasks:`, userProfile.tasks);

      // Use findOneAndUpdate to ensure atomic update
      try {
        const updatedProfile = await UserProfile.findOneAndUpdate(
          { anilistId: userId },
          {
            $set: {
              xp: userProfile.xp,
              level: userProfile.level,
              'tasks.lastReset': userProfile.tasks.lastReset
            },
            $push: {
              'tasks.completedTasks': taskId
            }
          },
          { new: true, runValidators: true }
        );

        if (!updatedProfile) {
          throw new Error('Profile not found during update');
        }

        console.log(`User profile updated successfully for ${userId} using findOneAndUpdate`);
        console.log(`Updated profile tasks:`, updatedProfile.tasks);

        // Use the updated profile for the response
        userProfile = updatedProfile;
      } catch (updateError) {
        console.error(`Error updating user profile for ${userId} with findOneAndUpdate:`, updateError);

        // Fallback to traditional save method
        try {
          await userProfile.save();
          console.log(`User profile saved successfully for ${userId} using traditional save`);

          // Double-check that the save worked by fetching the profile again
          const checkProfile = await UserProfile.findOne({ anilistId: userId });
          console.log(`Profile after save check for ${userId}:`, {
            completedTasks: checkProfile.tasks.completedTasks,
            xp: checkProfile.xp,
            level: checkProfile.level
          });
        } catch (saveError) {
          console.error(`Error saving user profile for ${userId}:`, saveError);

          // Try one more approach - direct update with $addToSet
          try {
            const directUpdate = await UserProfile.updateOne(
              { anilistId: userId },
              {
                $set: {
                  xp: userProfile.xp,
                  level: userProfile.level,
                  'tasks.lastReset': userProfile.tasks.lastReset
                },
                $addToSet: {
                  'tasks.completedTasks': taskId
                }
              }
            );

            console.log(`Direct update result for ${userId}:`, directUpdate);

            if (directUpdate.modifiedCount === 0) {
              throw new Error('No documents modified during direct update');
            }

            // Fetch the updated profile
            const updatedProfile = await UserProfile.findOne({ anilistId: userId });
            if (!updatedProfile) {
              throw new Error('Profile not found after direct update');
            }

            console.log(`Profile after direct update for ${userId}:`, {
              completedTasks: updatedProfile.tasks.completedTasks,
              xp: updatedProfile.xp,
              level: updatedProfile.level
            });

            // Use the updated profile for the response
            userProfile = updatedProfile;
          } catch (directUpdateError) {
            console.error(`Error with direct update for ${userId}:`, directUpdateError);
            return res.status(500).json({ message: 'Error updating user profile' });
          }
        }
      }

      res.status(200).json({
        message: 'Task completed successfully',
        completedTasks: userProfile.tasks.completedTasks,
        xpEarned: xpReward,
        xp: userProfile.xp,
        level: userProfile.level
      });
    } catch (error) {
      console.error('Error completing task:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // Unlock achievement
  unlockAchievement: async (req, res) => {
    try {
      const { userId } = req.params;
      const { achievementId } = req.body;

      if (!achievementId) {
        return res.status(400).json({ message: 'Achievement ID is required' });
      }

      // Find user profile
      let userProfile = await UserProfile.findOne({ anilistId: userId });

      if (!userProfile) {
        userProfile = new UserProfile({
          anilistId: userId
        });
      }

      // Check if achievement is already unlocked
      if (userProfile.achievements && userProfile.achievements.some(a => a.id === achievementId)) {
        return res.status(400).json({ message: 'Achievement already unlocked' });
      }

      // Create achievement object
      const achievement = {
        id: achievementId,
        title: achievementId, // This should be replaced with a proper title
        description: 'Achievement unlocked', // This should be replaced with a proper description
        date: new Date(),
        type: 'custom'
      };

      // Add achievement
      if (!userProfile.achievements) {
        userProfile.achievements = [];
      }
      userProfile.achievements.push(achievement);

      await userProfile.save();

      res.status(200).json({
        message: 'Achievement unlocked successfully',
        achievement
      });
    } catch (error) {
      console.error('Error unlocking achievement:', error);
      res.status(500).json({ message: error.message });
    }
  }
};

export default userProfileController;
