import { useState, useEffect, useRef } from "react";
import { X, Check, Image as ImageIcon } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Banner categories with their images
// These will be replaced with your CDN links
const BANNER_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    banners: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    banners: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    banners: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const BannerSelectionModal = ({ isOpen, onClose, onSelect, currentBanner }) => {
  const { user } = useAniList();
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [activeCategory, setActiveCategory] = useState(BANNER_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Touch handling for swipe to close
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const bottomSheetRef = useRef(null);

  // Set the current banner as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentBanner) {
      setSelectedBanner(currentBanner);
    }
    if (isOpen) {
      setIsAnimating(true);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, currentBanner]);

  // Handle banner selection
  const handleBannerSelect = (bannerUrl) => {
    setSelectedBanner(bannerUrl);
  };

  // Handle close with animation
  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300); // Match animation duration
  };

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Touch handlers for swipe to close
  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientY);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isDownSwipe = distance < -50; // Swipe down threshold

    if (isDownSwipe) {
      handleClose();
    }
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedBanner) return;

    setIsSaving(true);
    try {
      // Save the selected banner to the user's profile
      await profileApi.updateProfileBanner(user.id, selectedBanner);

      // Call the onSelect callback with the selected banner
      onSelect(selectedBanner);

      toast.success("Profile banner updated successfully!");
      handleClose();
    } catch (error) {
      console.error("Error updating profile banner:", error);
      toast.error("Failed to update profile banner. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 z-50 transition-all duration-300 ${
        isAnimating ? 'bg-black/80' : 'bg-black/0'
      }`}
      onClick={handleBackdropClick}
    >
      {/* Bottom Sheet */}
      <div
        ref={bottomSheetRef}
        className={`fixed bottom-0 left-0 right-0 mx-auto max-w-md bg-black/90 backdrop-blur-xl border border-white/10 border-b-0 shadow-[0_-10px_40px_rgba(0,0,0,0.8)] transition-all duration-300 ease-out ${
          isAnimating ? 'translate-y-0' : 'translate-y-full'
        }`}
        style={{
          borderTopLeftRadius: '24px',
          borderTopRightRadius: '24px',
          height: '75vh',
          maxHeight: '650px'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Glass panel effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-white/5 to-transparent pointer-events-none rounded-t-3xl"></div>

        {/* Drag handle */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-10 h-1 bg-white/30 rounded-full"></div>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between px-6 py-3 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 rounded-full bg-white"></div>
            <h2 className="text-lg font-bold text-white">Choose Banner</h2>
          </div>
          <button
            onClick={handleClose}
            className="p-2 rounded-full text-white/60 hover:text-white hover:bg-white/10 transition-all duration-200"
          >
            <X size={20} />
          </button>
        </div>

        {/* Preview section */}
        <div className="px-6 py-4 border-b border-white/10">
          {selectedBanner ? (
            <div className="relative h-24 rounded-lg overflow-hidden border border-white/10">
              <img
                src={selectedBanner}
                alt="Selected banner"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
              <div className="absolute bottom-2 left-2 flex items-center gap-2">
                <div className="w-8 h-8 rounded-full border border-white/20 overflow-hidden">
                  <img
                    src={user?.avatar?.large || user?.avatar?.medium || "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png"}
                    alt="User avatar"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="text-white">
                  <div className="text-xs font-bold">{user?.name || "Username"}</div>
                </div>
              </div>
              <div className="absolute bottom-2 right-2 bg-white rounded-full p-1">
                <Check size={12} className="text-black" />
              </div>
            </div>
          ) : (
            <div className="h-24 rounded-lg border-2 border-dashed border-white/10 flex flex-col items-center justify-center bg-white/5">
              <ImageIcon size={20} className="text-white/30 mb-1" />
              <p className="text-white/60 text-xs">No banner selected</p>
            </div>
          )}

          <div className="mt-3">
            <p className="text-white font-medium">
              {selectedBanner ? "Banner Selected" : "Select a Banner"}
            </p>
            <p className="text-white/60 text-sm">
              {selectedBanner ? "Tap Apply to save changes" : "Choose from the options below"}
            </p>
          </div>
        </div>

        {/* Category tabs */}
        <div className="border-b border-white/10 overflow-x-auto scrollbar-none">
          <div className="flex px-6">
            {BANNER_CATEGORIES.map(category => (
              <button
                key={category.id}
                className={`px-4 py-3 text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                  activeCategory === category.id
                    ? "text-white border-b-2 border-white"
                    : "text-white/60 hover:text-white"
                }`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Banner gallery */}
        <div className="flex-1 p-6 overflow-y-auto scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
          <div className="grid grid-cols-1 gap-4">
            {BANNER_CATEGORIES.find(c => c.id === activeCategory)?.banners.map(banner => (
              <button
                key={banner.id}
                className={`relative w-full aspect-[21/9] rounded-lg overflow-hidden transition-all duration-200 ${
                  selectedBanner === banner.url
                    ? "ring-2 ring-white scale-[1.02]"
                    : "ring-1 ring-white/10 hover:ring-white/30 hover:scale-[1.02]"
                }`}
                onClick={() => handleBannerSelect(banner.url)}
              >
                <img
                  src={banner.url}
                  alt={`Banner ${banner.id}`}
                  className="w-full h-full object-cover"
                />
                <div className={`absolute inset-0 ${
                  selectedBanner === banner.url
                    ? "bg-gradient-to-t from-black/60 to-transparent"
                    : "bg-gradient-to-t from-black/60 to-transparent opacity-0 hover:opacity-100"
                } transition-all duration-200`}>
                  <div className="absolute bottom-0 left-0 right-0 p-3 flex justify-between items-center">
                    <span className="text-white text-sm font-medium">
                      {selectedBanner === banner.url ? "Selected" : "Select"}
                    </span>
                    {selectedBanner === banner.url && (
                      <div className="bg-white rounded-full p-1">
                        <Check size={14} className="text-black" />
                      </div>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10 bg-black/50">
          <div className="flex gap-3">
            <button
              onClick={handleClose}
              className="flex-1 py-3 px-4 rounded-xl bg-white/10 hover:bg-white/20 text-white font-medium transition-all duration-200"
            >
              Cancel
            </button>

            <button
              onClick={handleSave}
              disabled={!selectedBanner || isSaving}
              className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-200 ${
                !selectedBanner || isSaving
                  ? "bg-white/20 text-white/50 cursor-not-allowed"
                  : "bg-white text-black hover:bg-white/90"
              }`}
            >
              {isSaving ? "Saving..." : "Apply"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerSelectionModal;
