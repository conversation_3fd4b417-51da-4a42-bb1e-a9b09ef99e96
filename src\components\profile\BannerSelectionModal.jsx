import { useState, useEffect } from "react";
import { X, Check, Image as ImageIcon, ChevronRight, ArrowRight } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Banner categories with their images
// These will be replaced with your CDN links
const BANNER_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    banners: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    banners: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    banners: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const BannerSelectionModal = ({ isOpen, onClose, onSelect, currentBanner }) => {
  const { user } = useAniList();
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [activeCategory, setActiveCategory] = useState(BANNER_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);

  // Set the current banner as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentBanner) {
      setSelectedBanner(currentBanner);
    }
  }, [isOpen, currentBanner]);

  // Handle banner selection
  const handleBannerSelect = (bannerUrl) => {
    setSelectedBanner(bannerUrl);
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedBanner) return;

    setIsSaving(true);
    try {
      // Save the selected banner to the user's profile
      await profileApi.updateProfileBanner(user.id, selectedBanner);

      // Call the onSelect callback with the selected banner
      onSelect(selectedBanner);

      toast.success("Profile banner updated successfully!");
      onClose();
    } catch (error) {
      console.error("Error updating profile banner:", error);
      toast.error("Failed to update profile banner. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/95">
      {/* Fixed-size modal - not responsive */}
      <div className="w-[1000px] h-[650px] overflow-hidden rounded-none border border-white/10 shadow-[0_0_40px_rgba(0,0,0,0.9)] bg-black">
        {/* Glass panel effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none"></div>

        {/* Top bar with title and close button */}
        <div className="h-12 bg-black flex items-center justify-between px-6 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 rounded-full bg-white"></div>
            <h2 className="text-sm font-bold text-white tracking-[0.2em]">SELECT BANNER</h2>
          </div>
          <button
            onClick={onClose}
            className="text-white/60 hover:text-white transition-all duration-200"
          >
            <X size={18} />
          </button>
        </div>

        <div className="flex flex-col h-[calc(650px-48px)]">
          {/* Preview section */}
          <div className="h-[180px] bg-black border-b border-white/10 relative">
            {selectedBanner ? (
              <div className="w-full h-full relative">
                <img
                  src={selectedBanner}
                  alt="Selected banner"
                  className="w-full h-full object-cover"
                />

                {/* Overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>

                {/* Preview badge */}
                <div className="absolute top-4 right-4 bg-black/70 backdrop-blur-md px-3 py-1.5 rounded-none text-xs text-white/90 border border-white/10 flex items-center gap-2">
                  <span className="w-2 h-2 rounded-full bg-white animate-pulse"></span>
                  <span>PREVIEW MODE</span>
                </div>

                {/* User profile mockup */}
                <div className="absolute bottom-4 left-6 flex items-center">
                  <div className="w-14 h-14 rounded-full border-2 border-white/10 overflow-hidden shadow-lg">
                    <img
                      src={user?.avatar?.large || user?.avatar?.medium || "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png"}
                      alt="User avatar"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="ml-3 text-white">
                    <div className="text-sm font-bold">{user?.name || "Username"}</div>
                    <div className="text-xs text-white/60">Profile Preview</div>
                  </div>
                </div>

                {/* Selection indicator */}
                <div className="absolute bottom-4 right-6 bg-white/10 backdrop-blur-md px-3 py-1.5 rounded-none text-xs text-white flex items-center gap-2 border border-white/10">
                  <Check size={12} className="text-white" />
                  <span>BANNER SELECTED</span>
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-r from-gray-900 to-black">
                <div className="w-16 h-16 rounded-full bg-white/5 border border-white/10 flex items-center justify-center mb-3">
                  <ImageIcon size={24} className="text-white/40" />
                </div>
                <p className="text-white/60 text-sm">No banner selected</p>
                <p className="text-white/40 text-xs mt-1">Choose from the options below</p>
              </div>
            )}
          </div>

          {/* Selection area */}
          <div className="flex-1 flex flex-col">
            {/* Category tabs */}
            <div className="h-10 border-b border-white/10 flex items-center px-2">
              {BANNER_CATEGORIES.map(category => (
                <button
                  key={category.id}
                  className={`px-4 h-10 text-xs font-medium transition-all duration-200 ${
                    activeCategory === category.id
                      ? "bg-white/10 text-white"
                      : "text-white/50 hover:text-white/80 hover:bg-white/5"
                  }`}
                  onClick={() => setActiveCategory(category.id)}
                >
                  {category.name.toUpperCase()}
                </button>
              ))}
            </div>

            {/* Banner gallery */}
            <div className="flex-1 p-6 overflow-y-auto scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
              <div className="grid grid-cols-2 gap-6">
                {BANNER_CATEGORIES.find(c => c.id === activeCategory)?.banners.map(banner => (
                  <div
                    key={banner.id}
                    className="group"
                  >
                    <button
                      className={`relative w-full overflow-hidden aspect-[21/9] transition-all duration-200 ${
                        selectedBanner === banner.url
                          ? "ring-2 ring-white"
                          : "ring-1 ring-white/10 hover:ring-white/30"
                      }`}
                      onClick={() => handleBannerSelect(banner.url)}
                    >
                      <img
                        src={banner.url}
                        alt={`Banner ${banner.id}`}
                        className="w-full h-full object-cover"
                      />

                      {/* Selection overlay */}
                      <div className={`absolute inset-0 ${
                        selectedBanner === banner.url
                          ? "bg-gradient-to-t from-black/70 to-transparent"
                          : "bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100"
                      } transition-all duration-200`}>
                        <div className="absolute bottom-0 left-0 right-0 p-3 flex justify-between items-center">
                          <span className="text-white text-xs">
                            {selectedBanner === banner.url ? "SELECTED" : "SELECT THIS BANNER"}
                          </span>
                          {selectedBanner === banner.url && (
                            <div className="bg-white rounded-full p-1">
                              <Check size={12} className="text-black" />
                            </div>
                          )}
                        </div>
                      </div>
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Footer with actions */}
            <div className="h-12 border-t border-white/10 flex items-center justify-between px-6">
              <div className="text-xs text-white/40">
                {selectedBanner ? "1 banner selected" : "No selection"}
              </div>

              <div className="flex items-center gap-4">
                <button
                  onClick={onClose}
                  className="text-white/50 hover:text-white text-xs transition-all duration-200"
                >
                  CANCEL
                </button>

                <button
                  onClick={handleSave}
                  disabled={!selectedBanner || isSaving}
                  className={`px-4 py-1.5 text-xs font-medium transition-all duration-200 ${
                    !selectedBanner || isSaving
                      ? "bg-white/10 text-white/30 cursor-not-allowed"
                      : "bg-white text-black hover:bg-white/90"
                  }`}
                >
                  {isSaving ? "SAVING..." : "APPLY SELECTION"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerSelectionModal;
