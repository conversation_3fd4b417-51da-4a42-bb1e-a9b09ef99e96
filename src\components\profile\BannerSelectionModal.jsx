import { useState, useEffect } from "react";
import { X, Check } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Banner categories with their images
// These will be replaced with your CDN links
const BANNER_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    banners: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    banners: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    banners: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const BannerSelectionModal = ({ isOpen, onClose, onSelect, currentBanner }) => {
  const { user } = useAniList();
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [activeCategory, setActiveCategory] = useState(BANNER_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);

  // Set the current banner as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentBanner) {
      setSelectedBanner(currentBanner);
    }
  }, [isOpen, currentBanner]);

  // Handle banner selection
  const handleBannerSelect = (bannerUrl) => {
    setSelectedBanner(bannerUrl);
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedBanner) return;

    setIsSaving(true);
    try {
      // Save the selected banner to the user's profile
      await profileApi.updateProfileBanner(user.id, selectedBanner);

      // Call the onSelect callback with the selected banner
      onSelect(selectedBanner);

      toast.success("Profile banner updated successfully!");
      onClose();
    } catch (error) {
      console.error("Error updating profile banner:", error);
      toast.error("Failed to update profile banner. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80">
      <div className="bg-gray-900 rounded-lg w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-white/10 flex justify-between items-center">
          <h2 className="text-xl font-bold">Background Image Selection</h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-white/10"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4">
          <p className="text-white/60 mb-4">Choose your profile background image</p>

          {/* Preview of selected banner */}
          {selectedBanner && (
            <div className="mb-6">
              <div className="w-full h-32 rounded-lg overflow-hidden border-2 border-white/10">
                <img
                  src={selectedBanner}
                  alt="Selected banner"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          )}

          {/* Category tabs */}
          <div className="flex border-b border-white/10 mb-4">
            {BANNER_CATEGORIES.map(category => (
              <button
                key={category.id}
                className={`px-4 py-2 text-sm font-medium ${
                  activeCategory === category.id
                    ? "text-white border-b-2 border-blue-500"
                    : "text-white/60 hover:text-white/80"
                }`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Banner grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 overflow-y-auto max-h-[40vh] p-2">
            {BANNER_CATEGORIES.find(c => c.id === activeCategory)?.banners.map(banner => (
              <button
                key={banner.id}
                className={`relative rounded-lg overflow-hidden border-2 aspect-[16/5] ${
                  selectedBanner === banner.url
                    ? "border-blue-500"
                    : "border-white/10 hover:border-white/30"
                }`}
                onClick={() => handleBannerSelect(banner.url)}
              >
                <img
                  src={banner.url}
                  alt={`Banner ${banner.id}`}
                  className="w-full h-full object-cover"
                />
                {selectedBanner === banner.url && (
                  <div className="absolute inset-0 bg-blue-500/30 flex items-center justify-center">
                    <Check size={24} className="text-white" />
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Footer with action buttons */}
        <div className="p-4 border-t border-white/10 flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded bg-white/10 hover:bg-white/20 text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!selectedBanner || isSaving}
            className={`px-4 py-2 rounded bg-blue-600 hover:bg-blue-700 text-sm font-medium ${
              !selectedBanner || isSaving ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {isSaving ? "Saving..." : "Done"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BannerSelectionModal;
