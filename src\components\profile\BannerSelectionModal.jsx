import { useState, useEffect } from "react";
import { X, Check, Image as ImageIcon, ChevronRight, ArrowRight } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Banner categories with their images
// These will be replaced with your CDN links
const BANNER_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    banners: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    banners: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    banners: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const BannerSelectionModal = ({ isOpen, onClose, onSelect, currentBanner }) => {
  const { user } = useAniList();
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [activeCategory, setActiveCategory] = useState(BANNER_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);

  // Set the current banner as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentBanner) {
      setSelectedBanner(currentBanner);
    }
  }, [isOpen, currentBanner]);

  // Handle banner selection
  const handleBannerSelect = (bannerUrl) => {
    setSelectedBanner(bannerUrl);
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedBanner) return;

    setIsSaving(true);
    try {
      // Save the selected banner to the user's profile
      await profileApi.updateProfileBanner(user.id, selectedBanner);

      // Call the onSelect callback with the selected banner
      onSelect(selectedBanner);

      toast.success("Profile banner updated successfully!");
      onClose();
    } catch (error) {
      console.error("Error updating profile banner:", error);
      toast.error("Failed to update profile banner. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/95">
      <div className="w-full max-w-5xl overflow-hidden rounded-xl border border-white/5 shadow-[0_0_40px_rgba(0,0,0,0.9)] bg-black">
        {/* Header with close button */}
        <div className="relative h-16 flex items-center px-6 border-b border-white/5 bg-gradient-to-r from-black to-gray-900">
          <h2 className="text-lg font-bold text-white tracking-wide">PROFILE BANNER</h2>
          <button
            onClick={onClose}
            className="absolute right-4 p-2 rounded-full hover:bg-white/5 transition-all duration-200 text-white/70 hover:text-white"
          >
            <X size={18} />
          </button>
        </div>

        <div className="flex flex-col h-[75vh]">
          {/* Preview section */}
          <div className="relative h-48 bg-black border-b border-white/5">
            {selectedBanner ? (
              <div className="w-full h-full relative group">
                <img
                  src={selectedBanner}
                  alt="Selected banner"
                  className="w-full h-full object-cover"
                />

                {/* Overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

                {/* Preview badge */}
                <div className="absolute top-4 left-4 bg-black/70 backdrop-blur-md px-3 py-1.5 rounded-md text-xs text-white/90 border border-white/10 flex items-center gap-2 shadow-lg">
                  <span className="w-2 h-2 rounded-full bg-white animate-pulse"></span>
                  <span>PREVIEW</span>
                </div>

                {/* User profile mockup */}
                <div className="absolute -bottom-6 left-6 flex items-end">
                  <div className="w-16 h-16 rounded-full border-4 border-black overflow-hidden shadow-lg z-10">
                    <img
                      src={user?.avatar?.large || user?.avatar?.medium || "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png"}
                      alt="User avatar"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="ml-2 mb-2 text-white">
                    <div className="text-sm font-bold">{user?.name || "Username"}</div>
                    <div className="text-xs text-white/60">Profile Banner Preview</div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-r from-gray-900 to-black">
                <div className="w-16 h-16 rounded-full bg-white/5 border border-white/10 flex items-center justify-center mb-3">
                  <ImageIcon size={24} className="text-white/40" />
                </div>
                <p className="text-white/60 text-sm">No banner selected</p>
                <p className="text-white/40 text-xs mt-1">Choose from the options below</p>
              </div>
            )}
          </div>

          {/* Selection area */}
          <div className="flex-1 flex flex-col">
            {/* Category tabs */}
            <div className="px-6 pt-6 pb-2">
              <div className="flex space-x-1 overflow-x-auto scrollbar-none">
                {BANNER_CATEGORIES.map(category => (
                  <button
                    key={category.id}
                    className={`px-4 py-2 text-sm font-medium rounded-md whitespace-nowrap transition-all duration-200 ${
                      activeCategory === category.id
                        ? "bg-white text-black"
                        : "bg-white/5 text-white/70 hover:bg-white/10 hover:text-white"
                    }`}
                    onClick={() => setActiveCategory(category.id)}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
              <div className="mt-4 flex justify-between items-center">
                <h3 className="text-white/80 text-sm font-medium">Select a banner for your profile</h3>
                {selectedBanner && (
                  <div className="flex items-center gap-2 text-white/60 text-xs">
                    <Check size={14} className="text-white" />
                    <span>1 banner selected</span>
                  </div>
                )}
              </div>
            </div>

            {/* Banner gallery */}
            <div className="flex-1 px-6 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {BANNER_CATEGORIES.find(c => c.id === activeCategory)?.banners.map(banner => (
                  <div
                    key={banner.id}
                    className="group"
                  >
                    <button
                      className={`relative w-full rounded-lg overflow-hidden aspect-[21/9] transition-all duration-300 ${
                        selectedBanner === banner.url
                          ? "ring-2 ring-white shadow-[0_0_15px_rgba(255,255,255,0.2)]"
                          : "ring-1 ring-white/5 hover:ring-white/20"
                      }`}
                      onClick={() => handleBannerSelect(banner.url)}
                    >
                      <img
                        src={banner.url}
                        alt={`Banner ${banner.id}`}
                        className="w-full h-full object-cover"
                      />

                      {/* Selection overlay */}
                      <div className={`absolute inset-0 flex items-center justify-center ${
                        selectedBanner === banner.url
                          ? "bg-black/30"
                          : "bg-black/60 opacity-0 group-hover:opacity-100"
                      } transition-all duration-300`}>
                        {selectedBanner === banner.url ? (
                          <div className="bg-white rounded-full p-2">
                            <Check size={20} className="text-black" />
                          </div>
                        ) : (
                          <div className="bg-white/20 backdrop-blur-md rounded-md px-4 py-2 text-sm text-white flex items-center gap-2">
                            <span>Select Banner</span>
                            <ArrowRight size={14} />
                          </div>
                        )}
                      </div>
                    </button>

                    {/* Selection indicator below image */}
                    {selectedBanner === banner.url && (
                      <div className="mt-2 flex items-center">
                        <div className="bg-white/10 rounded-md px-2 py-1 text-xs text-white/80 flex items-center gap-1">
                          <Check size={12} className="text-white" />
                          <span>Currently Selected</span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Footer with actions */}
            <div className="p-4 border-t border-white/5 bg-gradient-to-r from-black to-gray-900 flex justify-between items-center">
              <button
                onClick={onClose}
                className="text-white/50 hover:text-white text-sm transition-all duration-200"
              >
                Cancel
              </button>

              <button
                onClick={handleSave}
                disabled={!selectedBanner || isSaving}
                className={`flex items-center gap-2 px-5 py-2.5 rounded-md transition-all duration-200 ${
                  !selectedBanner || isSaving
                    ? "bg-white/20 text-white/50 cursor-not-allowed"
                    : "bg-white text-black hover:bg-white/90"
                }`}
              >
                {isSaving ? "Saving..." : "Apply Banner"}
                {!isSaving && <ChevronRight size={16} />}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerSelectionModal;
