import { useState, useEffect, useRef } from "react";
import { X, Check, Image as ImageIcon } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Banner categories with their images
// These will be replaced with your CDN links
const BANNER_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    banners: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr6", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr7", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr8", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr9", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr10", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr11", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr12", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    banners: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv6", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv7", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv8", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv9", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv10", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    banners: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot6", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot7", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot8", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot9", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot10", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot11", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot12", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot13", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot14", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const BannerSelectionModal = ({ isOpen, onClose, onSelect, currentBanner }) => {
  const { user } = useAniList();
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [activeCategory, setActiveCategory] = useState(BANNER_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Touch handling for swipe to close
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const bottomSheetRef = useRef(null);

  // Set the current banner as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentBanner) {
      setSelectedBanner(currentBanner);
    }
    if (isOpen) {
      setIsAnimating(true);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, currentBanner]);

  // Handle banner selection
  const handleBannerSelect = (bannerUrl) => {
    setSelectedBanner(bannerUrl);
  };

  // Handle close with animation
  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300); // Match animation duration
  };

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Touch handlers for swipe to close
  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientY);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isDownSwipe = distance < -50; // Swipe down threshold

    if (isDownSwipe) {
      handleClose();
    }
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedBanner) return;

    setIsSaving(true);
    try {
      // Save the selected banner to the user's profile
      await profileApi.updateProfileBanner(user.id, selectedBanner);

      // Call the onSelect callback with the selected banner
      onSelect(selectedBanner);

      toast.success("Profile banner updated successfully!");
      handleClose();
    } catch (error) {
      console.error("Error updating profile banner:", error);
      toast.error("Failed to update profile banner. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 z-50 transition-all duration-300 ${
        isAnimating ? 'bg-black/80' : 'bg-black/0'
      }`}
      onClick={handleBackdropClick}
    >
      {/* Bottom Sheet */}
      <div
        ref={bottomSheetRef}
        className={`fixed bottom-0 left-0 right-0 mx-auto max-w-md bg-black/90 backdrop-blur-xl border border-white/10 border-b-0 shadow-[0_-10px_40px_rgba(0,0,0,0.8)] transition-all duration-300 ease-out ${
          isAnimating ? 'translate-y-0' : 'translate-y-full'
        }`}
        style={{
          borderTopLeftRadius: '24px',
          borderTopRightRadius: '24px',
          height: '75vh',
          maxHeight: '650px'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Glass panel effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-white/5 to-transparent pointer-events-none rounded-t-3xl"></div>

        {/* Drag handle */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-10 h-1 bg-white/30 rounded-full"></div>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between px-6 py-3 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 rounded-full bg-white"></div>
            <h2 className="text-lg font-bold text-white">Choose Banner</h2>
          </div>
          <button
            onClick={handleClose}
            className="p-2 rounded-full text-white/60 hover:text-white hover:bg-white/10 transition-all duration-200"
          >
            <X size={20} />
          </button>
        </div>

        {/* Preview section */}
        <div className="px-6 py-4 border-b border-white/10">
          {selectedBanner ? (
            <div className="relative h-24 rounded-lg overflow-hidden border border-white/10">
              <img
                src={selectedBanner}
                alt="Selected banner"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
              <div className="absolute bottom-2 left-2 flex items-center gap-2">
                <div className="w-8 h-8 rounded-full border border-white/20 overflow-hidden">
                  <img
                    src={user?.avatar?.large || user?.avatar?.medium || "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png"}
                    alt="User avatar"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="text-white">
                  <div className="text-xs font-bold">{user?.name || "Username"}</div>
                </div>
              </div>
              <div className="absolute bottom-2 right-2 bg-white rounded-full p-1">
                <Check size={12} className="text-black" />
              </div>
            </div>
          ) : (
            <div className="h-24 rounded-lg border-2 border-dashed border-white/10 flex flex-col items-center justify-center bg-white/5">
              <ImageIcon size={20} className="text-white/30 mb-1" />
              <p className="text-white/60 text-xs">No banner selected</p>
            </div>
          )}

          <div className="mt-3">
            <p className="text-white font-medium">
              {selectedBanner ? "Banner Selected" : "Select a Banner"}
            </p>
            <p className="text-white/60 text-sm">
              {selectedBanner ? "Tap Apply to save changes" : "Choose from the options below"}
            </p>
          </div>
        </div>

        {/* Category tabs */}
        <div className="border-b border-white/10 overflow-x-auto scrollbar-none">
          <div className="flex px-6">
            {BANNER_CATEGORIES.map(category => (
              <button
                key={category.id}
                className={`px-4 py-3 text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                  activeCategory === category.id
                    ? "text-white border-b-2 border-white"
                    : "text-white/60 hover:text-white"
                }`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Banner gallery */}
        <div
          className="overflow-y-scroll px-4"
          style={{
            height: 'calc(100% - 160px)',
            scrollbarWidth: 'thin',
            scrollbarColor: 'rgba(255,255,255,0.2) transparent'
          }}
        >
          <div className="grid grid-cols-2 gap-3 py-4 pb-32">
            {BANNER_CATEGORIES.find(c => c.id === activeCategory)?.banners.map(banner => (
              <button
                key={banner.id}
                className={`group relative w-full aspect-[16/9] rounded-2xl overflow-hidden transition-all duration-300 ${
                  selectedBanner === banner.url
                    ? "ring-2 ring-cyan-400 shadow-lg shadow-cyan-400/25 scale-105"
                    : "ring-1 ring-white/20 hover:ring-cyan-400/50 hover:scale-105 hover:shadow-lg hover:shadow-white/10"
                }`}
                onClick={() => handleBannerSelect(banner.url)}
              >
                <img
                  src={banner.url}
                  alt={`Banner ${banner.id}`}
                  className="w-full h-full object-cover"
                />

                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />

                {/* Selection indicator */}
                <div className={`absolute inset-0 ${
                  selectedBanner === banner.url
                    ? "bg-gradient-to-t from-cyan-500/30 via-transparent to-transparent"
                    : ""
                } transition-all duration-300`}>
                  <div className="absolute bottom-0 left-0 right-0 p-3 flex justify-between items-center">
                    <span className="text-white text-sm font-semibold drop-shadow-lg">
                      {selectedBanner === banner.url ? "Selected" : "Select"}
                    </span>
                    {selectedBanner === banner.url && (
                      <div className="bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full p-1.5 shadow-lg animate-pulse">
                        <Check size={14} className="text-white" />
                      </div>
                    )}
                  </div>
                </div>

                {/* Hover effect */}
                <div className="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </button>
            ))}
          </div>
        </div>

        {/* Fixed Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black via-black/95 to-transparent backdrop-blur-xl border-t border-white/10">
          <div className="flex gap-3">
            <button
              onClick={handleClose}
              className="flex-1 py-4 px-6 rounded-2xl bg-gradient-to-r from-gray-800/80 to-gray-700/80 hover:from-gray-700/80 hover:to-gray-600/80 text-white font-semibold transition-all duration-300 border border-white/10 hover:border-white/20 backdrop-blur-sm"
            >
              Cancel
            </button>

            <button
              onClick={handleSave}
              disabled={!selectedBanner || isSaving}
              className={`flex-1 py-4 px-6 rounded-2xl font-semibold transition-all duration-300 ${
                !selectedBanner || isSaving
                  ? "bg-gradient-to-r from-gray-600/50 to-gray-500/50 text-white/50 cursor-not-allowed border border-white/5"
                  : "bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white shadow-lg shadow-cyan-500/25 hover:shadow-cyan-500/40 border border-cyan-400/20 hover:scale-105"
              }`}
            >
              {isSaving ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Saving...
                </div>
              ) : (
                "Apply"
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerSelectionModal;
