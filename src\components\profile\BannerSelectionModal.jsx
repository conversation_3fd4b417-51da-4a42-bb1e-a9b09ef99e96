import { useState, useEffect, useRef } from "react";
import { X, Check, Image as ImageIcon } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Banner categories with their images
// These will be replaced with your CDN links
const BANNER_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    banners: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr6", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr7", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr8", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr9", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr10", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr11", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr12", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    banners: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv6", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv7", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv8", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv9", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv10", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    banners: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot3", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot4", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot5", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot6", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot7", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot8", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot9", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot10", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot11", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot12", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot13", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot14", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const BannerSelectionModal = ({ isOpen, onClose, onSelect, currentBanner }) => {
  const { user } = useAniList();
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [activeCategory, setActiveCategory] = useState(BANNER_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Touch handling for swipe to close
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const bottomSheetRef = useRef(null);

  // Set the current banner as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentBanner) {
      setSelectedBanner(currentBanner);
    }
    if (isOpen) {
      setIsAnimating(true);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, currentBanner]);

  // Handle banner selection
  const handleBannerSelect = (bannerUrl) => {
    setSelectedBanner(bannerUrl);
  };

  // Handle close with animation
  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300); // Match animation duration
  };

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Touch handlers for swipe to close
  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientY);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isDownSwipe = distance < -50; // Swipe down threshold

    if (isDownSwipe) {
      handleClose();
    }
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedBanner) return;

    setIsSaving(true);
    try {
      // Save the selected banner to the user's profile
      await profileApi.updateProfileBanner(user.id, selectedBanner);

      // Call the onSelect callback with the selected banner
      onSelect(selectedBanner);

      toast.success("Profile banner updated successfully!");
      handleClose();
    } catch (error) {
      console.error("Error updating profile banner:", error);
      toast.error("Failed to update profile banner. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 z-50 transition-all duration-300 ${
        isAnimating ? 'bg-black/80' : 'bg-black/0'
      }`}
      onClick={handleBackdropClick}
    >
      {/* Bottom Sheet */}
      <div
        ref={bottomSheetRef}
        className={`fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm mx-4 bg-gradient-to-b from-gray-900/95 via-black/95 to-black/98 backdrop-blur-2xl border border-white/20 border-b-0 shadow-[0_-20px_60px_rgba(0,0,0,0.9)] transition-all duration-500 ease-out ${
          isAnimating ? 'translate-y-0' : 'translate-y-full'
        }`}
        style={{
          borderTopLeftRadius: '32px',
          borderTopRightRadius: '32px',
          height: '80vh',
          maxHeight: '700px',
          minHeight: '550px'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Glass panel effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-white/8 via-white/3 to-transparent pointer-events-none rounded-t-[32px]"></div>

        {/* Drag handle */}
        <div className="flex justify-center pt-4 pb-3">
          <div className="w-12 h-1.5 bg-gradient-to-r from-white/40 to-white/60 rounded-full shadow-lg"></div>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-white/20">
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 rounded-full bg-gradient-to-r from-cyan-400 to-blue-500 shadow-lg shadow-cyan-500/30"></div>
            <h2 className="text-xl font-bold text-white tracking-wide">Choose Banner</h2>
          </div>
          <button
            onClick={handleClose}
            className="p-3 rounded-full text-white/60 hover:text-white hover:bg-white/10 transition-all duration-300 hover:scale-110"
          >
            <X size={22} />
          </button>
        </div>

        {/* Preview section */}
        <div className="px-6 py-4 border-b border-white/10">
          {selectedBanner ? (
            <div className="relative h-24 rounded-lg overflow-hidden border border-white/10">
              <img
                src={selectedBanner}
                alt="Selected banner"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
              <div className="absolute bottom-2 left-2 flex items-center gap-2">
                <div className="w-8 h-8 rounded-full border border-white/20 overflow-hidden">
                  <img
                    src={user?.avatar?.large || user?.avatar?.medium || "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png"}
                    alt="User avatar"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="text-white">
                  <div className="text-xs font-bold">{user?.name || "Username"}</div>
                </div>
              </div>
              <div className="absolute bottom-2 right-2 bg-white rounded-full p-1">
                <Check size={12} className="text-black" />
              </div>
            </div>
          ) : (
            <div className="h-24 rounded-lg border-2 border-dashed border-white/10 flex flex-col items-center justify-center bg-white/5">
              <ImageIcon size={20} className="text-white/30 mb-1" />
              <p className="text-white/60 text-xs">No banner selected</p>
            </div>
          )}

          <div className="mt-3">
            <p className="text-white font-medium">
              {selectedBanner ? "Banner Selected" : "Select a Banner"}
            </p>
            <p className="text-white/60 text-sm">
              {selectedBanner ? "Tap Apply to save changes" : "Choose from the options below"}
            </p>
          </div>
        </div>

        {/* Category tabs */}
        <div className="border-b border-white/10 overflow-x-auto scrollbar-none">
          <div className="flex px-6">
            {BANNER_CATEGORIES.map(category => (
              <button
                key={category.id}
                className={`px-4 py-3 text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                  activeCategory === category.id
                    ? "text-white border-b-2 border-white"
                    : "text-white/60 hover:text-white"
                }`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Banner gallery */}
        <div
          className="overflow-y-scroll px-5 bg-gradient-to-b from-transparent to-black/20"
          style={{
            height: 'calc(100% - 180px)',
            scrollbarWidth: 'none',
            msOverflowStyle: 'none'
          }}
        >
          <style jsx>{`
            div::-webkit-scrollbar {
              display: none;
            }
          `}</style>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 py-6 pb-36">
            {BANNER_CATEGORIES.find(c => c.id === activeCategory)?.banners.map(banner => (
              <button
                key={banner.id}
                className={`group relative w-full aspect-[16/9] rounded-3xl overflow-hidden transition-all duration-500 transform-gpu ${
                  selectedBanner === banner.url
                    ? "ring-3 ring-cyan-400 shadow-2xl shadow-cyan-400/40 scale-105 z-10"
                    : "ring-2 ring-white/30 hover:ring-cyan-400/60 hover:scale-105 hover:shadow-xl hover:shadow-white/20"
                }`}
                onClick={() => handleBannerSelect(banner.url)}
              >
                <img
                  src={banner.url}
                  alt={`Banner ${banner.id}`}
                  className="w-full h-full object-cover"
                />

                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-70 group-hover:opacity-90 transition-all duration-500" />

                {/* Selection indicator */}
                <div className={`absolute inset-0 ${
                  selectedBanner === banner.url
                    ? "bg-gradient-to-t from-cyan-500/40 via-cyan-400/20 to-transparent"
                    : ""
                } transition-all duration-500`}>
                  <div className="absolute bottom-0 left-0 right-0 p-4 flex justify-between items-center">
                    <span className="text-white text-base font-bold drop-shadow-2xl">
                      {selectedBanner === banner.url ? "Selected" : "Select"}
                    </span>
                    {selectedBanner === banner.url && (
                      <div className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-500 rounded-full p-2 shadow-2xl animate-pulse">
                        <Check size={16} className="text-white drop-shadow-lg" />
                      </div>
                    )}
                  </div>
                </div>

                {/* Shimmer effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-500" />

                {/* Border glow */}
                <div className={`absolute inset-0 rounded-3xl ${
                  selectedBanner === banner.url
                    ? "bg-gradient-to-r from-cyan-400/20 via-blue-500/20 to-purple-500/20"
                    : "opacity-0 group-hover:opacity-100 bg-gradient-to-r from-white/10 to-white/5"
                } transition-all duration-500`} />
              </button>
            ))}
          </div>
        </div>

        {/* Fixed Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-5 bg-gradient-to-t from-black via-black/98 to-black/80 backdrop-blur-2xl border-t border-white/30">
          <div className="flex gap-4">
            <button
              onClick={handleClose}
              className="flex-1 py-5 px-6 rounded-3xl bg-gradient-to-r from-gray-800/90 to-gray-700/90 hover:from-gray-700/90 hover:to-gray-600/90 text-white font-bold transition-all duration-500 border-2 border-white/20 hover:border-white/40 backdrop-blur-sm shadow-xl hover:shadow-2xl hover:scale-105 active:scale-95"
            >
              Cancel
            </button>

            <button
              onClick={handleSave}
              disabled={!selectedBanner || isSaving}
              className={`flex-1 py-5 px-6 rounded-3xl font-bold transition-all duration-500 shadow-2xl ${
                !selectedBanner || isSaving
                  ? "bg-gradient-to-r from-gray-600/60 to-gray-500/60 text-white/50 cursor-not-allowed border-2 border-white/10"
                  : "bg-gradient-to-r from-cyan-500 via-blue-600 to-purple-600 hover:from-cyan-400 hover:via-blue-500 hover:to-purple-500 text-white shadow-cyan-500/30 hover:shadow-cyan-500/50 border-2 border-cyan-400/30 hover:border-cyan-300/50 hover:scale-105 active:scale-95"
              }`}
            >
              {isSaving ? (
                <div className="flex items-center justify-center gap-3">
                  <div className="w-5 h-5 border-3 border-white/40 border-t-white rounded-full animate-spin"></div>
                  <span>Saving...</span>
                </div>
              ) : (
                <span className="bg-gradient-to-r from-white to-white/90 bg-clip-text text-transparent">
                  Apply
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerSelectionModal;
