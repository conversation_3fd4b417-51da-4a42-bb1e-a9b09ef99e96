import { useState, useEffect } from "react";
import { X, Check } from "lucide-react";
import { useAniList } from "@/hooks/useAniList";
import profileApi from "@/api/profileApi";
import { toast } from "sonner";

// Banner categories with their images
// These will be replaced with your CDN links
const BANNER_CATEGORIES = [
  {
    id: "crunchyroll",
    name: "Crunchyroll",
    banners: [
      { id: "cr1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "cr2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "game-vault",
    name: "Game Vault",
    banners: [
      { id: "gv1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "gv2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  },
  {
    id: "attack-on-titan",
    name: "Attack on Titan",
    banners: [
      { id: "aot1", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
      { id: "aot2", url: "https://res.cloudinary.com/dygjmh0ft/image/upload/v1746714213/logo-hq_qxti4c.png" },
    ]
  }
];

const BannerSelectionModal = ({ isOpen, onClose, onSelect, currentBanner }) => {
  const { user } = useAniList();
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [activeCategory, setActiveCategory] = useState(BANNER_CATEGORIES[0].id);
  const [isSaving, setIsSaving] = useState(false);

  // Set the current banner as selected when the modal opens
  useEffect(() => {
    if (isOpen && currentBanner) {
      setSelectedBanner(currentBanner);
    }
  }, [isOpen, currentBanner]);

  // Handle banner selection
  const handleBannerSelect = (bannerUrl) => {
    setSelectedBanner(bannerUrl);
  };

  // Handle save button click
  const handleSave = async () => {
    if (!selectedBanner) return;

    setIsSaving(true);
    try {
      // Save the selected banner to the user's profile
      await profileApi.updateProfileBanner(user.id, selectedBanner);

      // Call the onSelect callback with the selected banner
      onSelect(selectedBanner);

      toast.success("Profile banner updated successfully!");
      onClose();
    } catch (error) {
      console.error("Error updating profile banner:", error);
      toast.error("Failed to update profile banner. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90">
      <div className="w-full max-w-5xl h-[85vh] overflow-hidden rounded-2xl border border-white/10 shadow-[0_0_30px_rgba(0,0,0,0.8)] flex flex-col">
        {/* Top section - Preview area */}
        <div className="relative h-1/3 min-h-[180px] bg-black">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 rounded-full bg-black/60 hover:bg-white/10 transition-all duration-200 text-white/80 hover:text-white z-10"
          >
            <X size={18} />
          </button>

          {/* Preview banner */}
          {selectedBanner ? (
            <div className="w-full h-full relative">
              <img
                src={selectedBanner}
                alt="Selected banner"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black via-black/30 to-transparent"></div>
              <div className="absolute bottom-4 left-4 bg-black/60 backdrop-blur-md px-3 py-1.5 rounded-full text-xs text-white/80 border border-white/10 flex items-center gap-2">
                <Check size={14} className="text-white" />
                Banner Preview
              </div>
            </div>
          ) : (
            <div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-r from-gray-900 to-black">
              <div className="w-16 h-16 rounded-full bg-white/5 border border-white/10 flex items-center justify-center mb-4">
                <ImageIcon size={24} className="text-white/40" />
              </div>
              <p className="text-white/60 text-sm">Select a banner from the gallery below</p>
            </div>
          )}
        </div>

        {/* Bottom section - Categories and Gallery */}
        <div className="flex-1 bg-black/80 backdrop-blur-xl flex flex-col">
          <div className="border-b border-white/10">
            <div className="container mx-auto px-6 py-4">
              <h2 className="text-xl font-bold text-white mb-1">Choose Your Banner</h2>
              <p className="text-white/60 text-sm">Select a background image for your profile</p>
            </div>
          </div>

          {/* Category pills */}
          <div className="container mx-auto px-6 pt-4 pb-2 flex flex-wrap gap-2">
            {BANNER_CATEGORIES.map(category => (
              <button
                key={category.id}
                className={`px-4 py-1.5 text-sm font-medium rounded-full transition-all duration-200 ${
                  activeCategory === category.id
                    ? "bg-white text-black"
                    : "bg-white/10 text-white/80 hover:bg-white/20"
                }`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Gallery */}
          <div className="flex-1 container mx-auto px-6 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-white/10 scrollbar-track-transparent">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {BANNER_CATEGORIES.find(c => c.id === activeCategory)?.banners.map(banner => (
                <div
                  key={banner.id}
                  className={`group cursor-pointer relative rounded-xl overflow-hidden aspect-[21/9] transition-all duration-300 ${
                    selectedBanner === banner.url
                      ? "ring-2 ring-white shadow-[0_0_15px_rgba(255,255,255,0.2)]"
                      : "hover:shadow-[0_0_20px_rgba(255,255,255,0.1)]"
                  }`}
                  onClick={() => handleBannerSelect(banner.url)}
                >
                  <img
                    src={banner.url}
                    alt={`Banner ${banner.id}`}
                    className="w-full h-full object-cover"
                  />

                  {/* Overlay */}
                  <div className={`absolute inset-0 transition-all duration-300 ${
                    selectedBanner === banner.url
                      ? "bg-gradient-to-t from-black/80 via-black/20 to-transparent"
                      : "bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100"
                  }`}>
                    <div className="absolute bottom-0 left-0 right-0 p-4 flex justify-between items-center">
                      <span className="text-white text-sm font-medium">
                        {selectedBanner === banner.url ? "Selected" : "Select this banner"}
                      </span>
                      {selectedBanner === banner.url && (
                        <div className="bg-white rounded-full p-1">
                          <Check size={16} className="text-black" />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Action buttons */}
          <div className="container mx-auto px-6 py-4 border-t border-white/10 flex justify-between items-center">
            <button
              onClick={onClose}
              className="px-4 py-2 text-white/70 hover:text-white transition-all duration-200"
            >
              Cancel
            </button>

            <button
              onClick={handleSave}
              disabled={!selectedBanner || isSaving}
              className={`px-6 py-2.5 rounded-lg bg-white text-black font-medium transition-all duration-200 ${
                !selectedBanner || isSaving
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:bg-white/90"
              }`}
            >
              {isSaving ? "Saving..." : "Apply Banner"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerSelectionModal;
